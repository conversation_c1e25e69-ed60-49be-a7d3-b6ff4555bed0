<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stagehand Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .search-box {
            margin: 20px 0;
            padding: 10px;
            width: 100%;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
            display: none;
        }
        .info-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 Stagehand Test Page</h1>
        
        <div class="info-box">
            <strong>Welcome!</strong> This is a test page for Stagehand browser automation.
        </div>

        <div>
            <label for="searchInput">Search Input:</label>
            <input type="text" id="searchInput" class="search-box" placeholder="Type something here...">
        </div>

        <div>
            <button class="button" onclick="performSearch()">Search</button>
            <button class="button" onclick="clearResults()">Clear</button>
            <button class="button" onclick="showInfo()">Show Info</button>
        </div>

        <div id="result" class="result">
            <h3>Search Result:</h3>
            <p id="resultText">No search performed yet.</p>
        </div>

        <div style="margin-top: 30px;">
            <h3>Available Actions:</h3>
            <ul>
                <li>Type in the search box</li>
                <li>Click the Search button</li>
                <li>Click the Clear button</li>
                <li>Click the Show Info button</li>
            </ul>
        </div>
    </div>

    <script>
        function performSearch() {
            const input = document.getElementById('searchInput');
            const result = document.getElementById('result');
            const resultText = document.getElementById('resultText');
            
            if (input.value.trim()) {
                resultText.textContent = `You searched for: "${input.value}"`;
                result.style.display = 'block';
            } else {
                alert('Please enter something to search!');
            }
        }

        function clearResults() {
            const input = document.getElementById('searchInput');
            const result = document.getElementById('result');
            
            input.value = '';
            result.style.display = 'none';
        }

        function showInfo() {
            alert('This is a test page for Stagehand browser automation framework!');
        }
    </script>
</body>
</html>
