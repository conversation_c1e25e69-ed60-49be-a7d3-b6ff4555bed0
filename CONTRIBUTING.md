# Contributing to NanoBrowser

We deeply appreciate your interest in contributing to NanoBrowser! Every contribution helps make Nanobrowser more powerful and accessible for everyone.

## Quick Start

1. Fork and clone the repository
2. Create a new branch (`git checkout -b feature-name`)
3. Make your changes
4. Submit a Pull Request

## How Can I Contribute?

### Reporting Bugs
- Search existing issues first
- Include:
  - Clear description
  - Steps to reproduce
  - Environment details (OS, browser version)
  - Screenshots if applicable

### Suggesting Enhancements
- Open an issue with a clear title and detailed description
- Explain why this enhancement would be useful

### Code Contributions
1. Follow the existing code style
2. Write clear commit messages in present tense ("Add feature" not "Added feature")
3. Test your changes thoroughly
4. Update documentation if needed
5. Create a Pull Request with a clear description
6. Be responsive to feedback and address review comments promptly

## License

By contributing, you agree that your contributions will be licensed under the project's license terms.
