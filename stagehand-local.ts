import { Stagehand } from "@browserbasehq/stagehand";
import { z } from "zod";
import path from "path";
import { fileURLToPath } from "url";

async function localDemo() {
  console.log("🎭 Starting Stagehand local file demo...");

  // 获取当前文件的目录
  const currentDir = process.cwd();
  const testHtmlPath = path.join(currentDir, "test.html");
  const fileUrl = `file://${testHtmlPath}`;

  console.log(`📁 Test file path: ${fileUrl}`);

  // 初始化 Stagehand 实例
  const stagehand = new Stagehand({
    env: "LOCAL", // 使用本地浏览器
    verbose: 1, // 启用详细日志
    enableCaching: false, // 禁用缓存
    localBrowserLaunchOptions: {
      headless: false, // 显示浏览器窗口
      viewport: {
        width: 1280,
        height: 720,
      },
    },
  });

  try {
    // 初始化浏览器
    await stagehand.init();
    console.log("✅ Stagehand initialized successfully!");

    const page = stagehand.page;

    // 加载本地 HTML 文件
    console.log("\n📍 Step 1: Loading local test page...");
    await page.goto(fileUrl);
    
    // 获取页面标题
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);

    // 等待页面加载
    console.log("\n⏳ Waiting for page to load...");
    await page.waitForTimeout(2000);

    // 使用基本的 Playwright 功能测试页面交互
    console.log("\n🔍 Step 2: Testing basic interactions...");
    
    // 在搜索框中输入文本
    console.log("✏️  Typing in search box...");
    await page.fill('#searchInput', 'Hello Stagehand!');
    
    // 点击搜索按钮
    console.log("🔍 Clicking search button...");
    await page.click('button:has-text("Search")');
    
    // 等待结果显示
    await page.waitForTimeout(1000);
    
    // 检查结果是否显示
    const resultVisible = await page.isVisible('#result');
    console.log(`📊 Search result visible: ${resultVisible}`);
    
    if (resultVisible) {
      const resultText = await page.textContent('#resultText');
      console.log(`📝 Result text: ${resultText}`);
    }

    // 测试清除功能
    console.log("\n🧹 Step 3: Testing clear functionality...");
    await page.click('button:has-text("Clear")');
    await page.waitForTimeout(1000);
    
    const inputValue = await page.inputValue('#searchInput');
    console.log(`📝 Input value after clear: "${inputValue}"`);

    // 测试信息按钮
    console.log("\n💡 Step 4: Testing info button...");
    
    // 设置对话框处理
    page.on('dialog', async dialog => {
      console.log(`🔔 Dialog appeared: ${dialog.message()}`);
      await dialog.accept();
    });
    
    await page.click('button:has-text("Show Info")');
    await page.waitForTimeout(1000);

    console.log("\n🎉 Local demo completed successfully!");
    console.log("💡 Basic Playwright functionality is working!");
    console.log("🚀 To test AI features, you would need to:");
    console.log("   1. Set OPENAI_API_KEY environment variable");
    console.log("   2. Use page.act(), page.observe(), and page.extract() methods");

  } catch (error) {
    console.error("❌ Error during demo:", error);
  } finally {
    // 清理资源
    console.log("\n🧹 Cleaning up...");
    await stagehand.close();
    console.log("✅ Stagehand closed successfully!");
  }
}

// 运行示例
localDemo().catch(console.error);
