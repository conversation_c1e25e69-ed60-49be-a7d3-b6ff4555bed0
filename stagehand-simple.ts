import { Stagehand } from "@browserbasehq/stagehand";

async function simpleDemo() {
  console.log("🎭 Starting simple Stagehand demo (without AI features)...");

  // 初始化 Stagehand 实例
  const stagehand = new Stagehand({
    env: "LOCAL", // 使用本地浏览器
    verbose: 1, // 启用详细日志
    enableCaching: false, // 禁用缓存
    localBrowserLaunchOptions: {
      headless: false, // 显示浏览器窗口
      viewport: {
        width: 1280,
        height: 720,
      },
    },
  });

  try {
    // 初始化浏览器
    await stagehand.init();
    console.log("✅ Stagehand initialized successfully!");

    const page = stagehand.page;

    // 基本的页面导航测试
    console.log("\n📍 Step 1: Navigating to example.com...");
    await page.goto("https://example.com");
    
    // 获取页面标题
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);

    // 获取页面 URL
    const url = page.url();
    console.log(`🔗 Current URL: ${url}`);

    // 等待一下让我们看到页面
    console.log("\n⏳ Waiting 3 seconds to observe the page...");
    await page.waitForTimeout(3000);

    // 导航到另一个简单的页面
    console.log("\n📍 Step 2: Navigating to httpbin.org...");
    await page.goto("https://httpbin.org");
    
    const newTitle = await page.title();
    console.log(`📄 New page title: ${newTitle}`);

    // 再等待一下
    console.log("\n⏳ Waiting 3 seconds...");
    await page.waitForTimeout(3000);

    // 使用基本的 Playwright 功能
    console.log("\n🔍 Step 3: Testing basic Playwright functionality...");
    
    // 检查页面是否包含某些文本
    const bodyText = await page.textContent('body');
    if (bodyText?.includes('httpbin')) {
      console.log("✅ Found expected content on the page!");
    } else {
      console.log("❌ Expected content not found");
    }

    console.log("\n🎉 Simple demo completed successfully!");
    console.log("💡 To use AI features, you'll need to set up an OpenAI API key:");
    console.log("   export OPENAI_API_KEY='your-api-key-here'");

  } catch (error) {
    console.error("❌ Error during demo:", error);
  } finally {
    // 清理资源
    console.log("\n🧹 Cleaning up...");
    await stagehand.close();
    console.log("✅ Stagehand closed successfully!");
  }
}

// 运行示例
simpleDemo().catch(console.error);
