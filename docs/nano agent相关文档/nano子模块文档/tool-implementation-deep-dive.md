# nanobrowser 工具实现深度解析
## 表单填写等核心功能的底层机制

通过深入代码分析，我来详细解析nanobrowser的工具是如何实际运行的，特别是表单填写等复杂操作的实现机制。

## 🔧 工具执行完整链路

### 1. **Action调用流程图**

```mermaid
sequenceDiagram
    participant AI as AI决策
    participant NA as NavigatorAgent
    participant AB as ActionBuilder
    participant A as Action
    participant P as Page
    participant DOM as DOM Layer
    participant Browser as 浏览器
    
    AI->>NA: 生成Action序列
    NA->>AB: 获取Action实例
    AB->>A: 创建Action对象
    A->>A: 参数验证(Zod)
    A->>P: 调用Page方法
    P->>DOM: 元素定位
    DOM->>Browser: 执行DOM操作
    Browser-->>DOM: 操作结果
    DOM-->>P: 返回状态
    P-->>A: ActionResult
    A-->>NA: 执行结果
    NA-->>AI: 反馈给AI
```

### 2. **核心Action实现架构**

```typescript
// Action基础架构
export class Action {
  constructor(
    private readonly handler: (input: any) => Promise<ActionResult>,
    public readonly schema: ActionSchema,
    public readonly hasIndex: boolean = false,
  ) {}

  async call(input: unknown): Promise<ActionResult> {
    // 1. 参数验证 - 使用Zod schema
    const parsedArgs = this.schema.schema.safeParse(input);
    if (!parsedArgs.success) {
      throw new InvalidInputError(parsedArgs.error.message);
    }
    
    // 2. 执行处理函数
    return await this.handler(parsedArgs.data);
  }
}
```

## 📝 表单填写工具详细实现

### 1. **input_text Action完整流程**

#### **Schema定义**
```typescript
export const inputTextActionSchema: ActionSchema = {
  name: 'input_text',
  description: 'Input text into an interactive input element',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    index: z.number().int().describe('index of the element'),
    text: z.string().describe('text to input'),
    xpath: z.string().nullable().optional().describe('xpath of the element'),
  }),
};
```

#### **Action Handler实现**
```typescript
const inputText = new Action(
  async (input: z.infer<typeof inputTextActionSchema.schema>) => {
    // 1. 事件发射 - 通知UI开始执行
    const intent = input.intent || `Input text into index ${input.index}`;
    this.context.emitEvent(Actors.NAVIGATOR, ExecutionState.ACT_START, intent);

    // 2. 获取当前页面和状态
    const page = await this.context.browserContext.getCurrentPage();
    const state = await page.getState();

    // 3. 通过索引获取DOM元素
    const elementNode = state?.selectorMap.get(input.index);
    if (!elementNode) {
      throw new Error(`Element with index ${input.index} does not exist`);
    }

    // 4. 执行文本输入操作
    await page.inputTextElementNode(this.context.options.useVision, elementNode, input.text);
    
    // 5. 返回结果
    const msg = `Input ${input.text} into index ${input.index}`;
    this.context.emitEvent(Actors.NAVIGATOR, ExecutionState.ACT_OK, msg);
    return new ActionResult({ extractedContent: msg, includeInMemory: true });
  },
  inputTextActionSchema,
  true, // hasIndex = true
);
```

#### **Page层文本输入实现**
```typescript
async inputTextElementNode(useVision: boolean, elementNode: DOMElementNode, text: string): Promise<void> {
  // 1. 元素定位
  const element = await this.locateElement(elementNode);
  if (!element) {
    throw new Error(`Element: ${elementNode} not found`);
  }

  // 2. 元素稳定性等待
  await this._waitForElementStability(element, 1500);

  // 3. 滚动到视图
  const isHidden = await element.isHidden();
  if (!isHidden) {
    await this._scrollIntoViewIfNeeded(element, 1500);
  }

  // 4. 获取元素属性
  const tagName = await element.evaluate(el => el.tagName.toLowerCase());
  const isContentEditable = await element.evaluate(el => {
    if (el instanceof HTMLElement) {
      return el.isContentEditable;
    }
    return false;
  });
  const isReadOnly = await element.evaluate(el => {
    if (el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement) {
      return el.readOnly;
    }
    return false;
  });

  // 5. 智能输入策略选择
  if ((isContentEditable || tagName === 'input') && !isReadOnly && !isDisabled) {
    // 策略A: 清空内容后逐字符输入
    await element.evaluate(el => {
      if (el instanceof HTMLElement) {
        el.textContent = '';
      }
      if ('value' in el) {
        (el as HTMLInputElement).value = '';
      }
      // 触发事件
      el.dispatchEvent(new Event('input', { bubbles: true }));
      el.dispatchEvent(new Event('change', { bubbles: true }));
    });

    // 模拟真实用户输入 - 50ms延迟
    await element.type(text, { delay: 50 });
  } else {
    // 策略B: 直接设置value属性
    await element.evaluate((el, value) => {
      if (el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement) {
        el.value = value;
      } else if (el instanceof HTMLElement && el.isContentEditable) {
        el.textContent = value;
      }
      // 触发事件
      el.dispatchEvent(new Event('input', { bubbles: true }));
      el.dispatchEvent(new Event('change', { bubbles: true }));
    }, text);
  }

  // 6. 等待页面稳定
  await this.waitForPageAndFramesLoad();
}
```

### 2. **下拉框操作工具实现**

#### **get_dropdown_options Action**
```typescript
const getDropdownOptions = new Action(
  async (input: z.infer<typeof getDropdownOptionsActionSchema.schema>) => {
    const intent = input.intent || `Getting options from dropdown with index ${input.index}`;
    this.context.emitEvent(Actors.NAVIGATOR, ExecutionState.ACT_START, intent);

    const page = await this.context.browserContext.getCurrentPage();
    const state = await page.getState();

    const elementNode = state?.selectorMap.get(input.index);
    if (!elementNode) {
      throw new Error(`Element with index ${input.index} does not exist`);
    }

    // 验证是否为select元素
    if (!elementNode.tagName || elementNode.tagName.toLowerCase() !== 'select') {
      throw new Error(`Element with index ${input.index} is not a SELECT element`);
    }

    try {
      // 获取所有选项
      const options = await page.getDropdownOptions(input.index);

      if (options && options.length > 0) {
        // 格式化选项供AI使用
        const formattedOptions: string[] = options.map(opt => {
          // JSON编码确保AI使用精确的字符串
          const encodedText = JSON.stringify(opt.text);
          return `${opt.index}: text=${encodedText}`;
        });

        let msg = formattedOptions.join('\n');
        msg += '\nUse the exact text string in select_dropdown_option';
        
        return new ActionResult({
          extractedContent: msg,
          includeInMemory: true,
        });
      }
    } catch (error) {
      const errorMsg = `Failed to get dropdown options: ${error.message}`;
      return new ActionResult({
        error: errorMsg,
        includeInMemory: true,
      });
    }
  },
  getDropdownOptionsActionSchema,
  true,
);
```

#### **Page层下拉框选项获取**
```typescript
async getDropdownOptions(index: number): Promise<Array<{ index: number; text: string; value: string }>> {
  const selectorMap = this.getSelectorMap();
  const element = selectorMap?.get(index);

  if (!element || !this._puppeteerPage) {
    throw new Error('Element not found or puppeteer is not connected');
  }

  try {
    // 定位select元素
    const elementHandle = await this.locateElement(element);
    if (!elementHandle) {
      throw new Error('Dropdown element not found');
    }

    // 在浏览器上下文中执行JavaScript获取选项
    const options = await elementHandle.evaluate(select => {
      if (!(select instanceof HTMLSelectElement)) {
        throw new Error('Element is not a select element');
      }

      // 提取所有option元素的信息
      return Array.from(select.options).map(option => ({
        index: option.index,
        text: option.text, // 保持原始文本，不trim
        value: option.value,
      }));
    });

    if (!options.length) {
      throw new Error('No options found in dropdown');
    }

    return options;
  } catch (error) {
    throw new Error(`Failed to get dropdown options: ${error.message}`);
  }
}
```

#### **select_dropdown_option Action**
```typescript
const selectDropdownOption = new Action(
  async (input: z.infer<typeof selectDropdownOptionActionSchema.schema>) => {
    const intent = input.intent || `Select option "${input.text}" from dropdown with index ${input.index}`;
    this.context.emitEvent(Actors.NAVIGATOR, ExecutionState.ACT_START, intent);

    const page = await this.context.browserContext.getCurrentPage();
    const state = await page.getState();

    const elementNode = state?.selectorMap.get(input.index);
    if (!elementNode) {
      const errorMsg = `Element with index ${input.index} does not exist`;
      return new ActionResult({ error: errorMsg, includeInMemory: true });
    }

    // 验证select元素
    if (!elementNode.tagName || elementNode.tagName.toLowerCase() !== 'select') {
      const errorMsg = `Cannot select option: Element with index ${input.index} is a ${elementNode.tagName}, not a SELECT`;
      return new ActionResult({ error: errorMsg, includeInMemory: true });
    }

    try {
      const result = await page.selectDropdownOption(input.index, input.text);
      const msg = `Selected option "${input.text}" from dropdown with index ${input.index}`;
      
      return new ActionResult({
        extractedContent: result,
        includeInMemory: true,
      });
    } catch (error) {
      const errorMsg = `Failed to select option: ${error.message}`;
      return new ActionResult({ error: errorMsg, includeInMemory: true });
    }
  },
  selectDropdownOptionActionSchema,
  true,
);
```

#### **Page层下拉框选择实现**
```typescript
async selectDropdownOption(index: number, text: string): Promise<string> {
  const selectorMap = this.getSelectorMap();
  const element = selectorMap?.get(index);

  if (!element || !this._puppeteerPage) {
    throw new Error('Element not found or puppeteer is not connected');
  }

  // 验证select元素
  if (element.tagName?.toLowerCase() !== 'select') {
    throw new Error(`Cannot select option: Element with index ${index} is a ${element.tagName}, not a SELECT`);
  }

  try {
    const elementHandle = await this.locateElement(element);
    if (!elementHandle) {
      throw new Error('Dropdown element not found');
    }

    // 在浏览器上下文中验证并选择选项
    const result = await elementHandle.evaluate(
      (select, optionText, elementIndex) => {
        if (!(select instanceof HTMLSelectElement)) {
          return {
            found: false,
            message: `Element with index ${elementIndex} is not a SELECT`,
          };
        }

        const options = Array.from(select.options);
        const option = options.find(opt => opt.text.trim() === optionText);

        if (!option) {
          const availableOptions = options.map(o => o.text.trim()).join('", "');
          return {
            found: false,
            message: `Option "${optionText}" not found. Available options: "${availableOptions}"`,
          };
        }

        // 选择选项
        select.selectedIndex = option.index;
        
        // 触发change事件
        select.dispatchEvent(new Event('change', { bubbles: true }));
        select.dispatchEvent(new Event('input', { bubbles: true }));

        return {
          found: true,
          message: `Successfully selected option "${optionText}"`,
        };
      },
      text,
      index,
    );

    if (!result.found) {
      throw new Error(result.message);
    }

    return result.message;
  } catch (error) {
    throw new Error(`Failed to select dropdown option: ${error.message}`);
  }
}
```

## 🎯 DOM操作核心机制

### 1. **元素定位策略**

```typescript
async locateElement(element: DOMElementNode): Promise<ElementHandle | null> {
  if (!this._puppeteerPage) {
    return null;
  }
  
  let currentFrame: PuppeteerPage | Frame = this._puppeteerPage;

  // 1. 处理iframe嵌套
  const parents: DOMElementNode[] = [];
  let current = element;
  while (current.parent) {
    parents.push(current.parent);
    current = current.parent;
  }

  // 2. 从根节点向下定位
  parents.reverse();
  for (const parent of parents) {
    if (parent.tagName === 'iframe') {
      const iframeHandle = await currentFrame.$(parent.getCssSelector());
      if (iframeHandle) {
        currentFrame = await iframeHandle.contentFrame();
      }
    }
  }

  // 3. 多重定位策略
  try {
    // 策略1: CSS选择器
    const cssSelector = element.getCssSelector();
    let elementHandle = await currentFrame.$(cssSelector);
    
    // 策略2: XPath选择器（回退）
    if (!elementHandle && element.xpath) {
      const xpathSelector = `::-p-xpath(${element.xpath})`;
      elementHandle = await currentFrame.$(xpathSelector);
    }
    
    // 策略3: 检查可见性并滚动到视图
    if (elementHandle) {
      const isHidden = await elementHandle.isHidden();
      if (!isHidden) {
        await this._scrollIntoViewIfNeeded(elementHandle);
      }
    }
    
    return elementHandle;
  } catch (error) {
    logger.error('Failed to locate element:', error);
    return null;
  }
}
```

### 2. **智能滚动机制**

```typescript
private async _scrollIntoViewIfNeeded(element: ElementHandle, timeout = 1000): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    // 检查元素是否在视口内
    const isVisible = await element.evaluate(el => {
      const rect = el.getBoundingClientRect();
      const isInViewport = (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.visualViewport?.height || window.innerHeight) &&
        rect.right <= (window.visualViewport?.width || window.innerWidth)
      );

      if (!isInViewport) {
        // 滚动到视图中心
        el.scrollIntoView({
          behavior: 'auto',
          block: 'center',
          inline: 'center',
        });
        return false;
      }

      return true;
    });

    if (isVisible) break;

    // 超时处理
    if (Date.now() - startTime > timeout) {
      logger.warning('Timed out while trying to scroll element into view, continuing anyway');
      break;
    }

    // 小延迟后重试
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}
```

### 3. **元素稳定性等待**

```typescript
private async _waitForElementStability(element: ElementHandle, timeout = 1000): Promise<void> {
  const startTime = Date.now();
  let lastRect = await element.boundingBox();
  
  while (Date.now() - startTime < timeout) {
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const currentRect = await element.boundingBox();
    
    // 检查位置和大小是否稳定
    if (lastRect && currentRect) {
      const positionChanged = 
        Math.abs(lastRect.x - currentRect.x) > 1 ||
        Math.abs(lastRect.y - currentRect.y) > 1 ||
        Math.abs(lastRect.width - currentRect.width) > 1 ||
        Math.abs(lastRect.height - currentRect.height) > 1;
      
      if (!positionChanged) {
        // 元素稳定，可以操作
        return;
      }
    }
    
    lastRect = currentRect;
  }
  
  // 超时但继续执行
  logger.warning('Element stability timeout, proceeding anyway');
}
```

## 🔍 DOM树构建与元素识别

### 1. **buildDomTree核心算法**

```javascript
function buildDomTree(node, parentIframe = null, isParentHighlighted = false) {
  // 1. 快速过滤检查
  if (!node || node.id === HIGHLIGHT_CONTAINER_ID || 
      (node.nodeType !== Node.ELEMENT_NODE && node.nodeType !== Node.TEXT_NODE)) {
    return null;
  }

  // 2. 视口裁剪优化
  if (node.nodeType === Node.ELEMENT_NODE) {
    const rect = getCachedBoundingClientRect(node);
    const isFixedOrSticky = isElementFixedOrSticky(node);
    const hasSize = rect && (rect.width > 0 || rect.height > 0);
    
    // 快速排除视口外元素
    if (!rect || (!isFixedOrSticky && !hasSize && 
        (rect.bottom < -viewportExpansion || rect.top > window.innerHeight + viewportExpansion))) {
      return null;
    }
  }

  // 3. 构建节点数据
  const nodeData = {
    tagName: node.tagName.toLowerCase(),
    attributes: {},
    xpath: getXPathTree(node, true),
    children: [],
  };

  // 4. 可见性和交互性检测
  if (node.nodeType === Node.ELEMENT_NODE) {
    nodeData.isVisible = isElementVisible(node);
    if (nodeData.isVisible) {
      nodeData.isTopElement = isTopElement(node);
      if (nodeData.isTopElement) {
        nodeData.isInteractive = isInteractiveElement(node);
        // 高亮处理
        nodeWasHighlighted = handleHighlighting(nodeData, node, parentIframe, isParentHighlighted);
      }
    }
  }

  // 5. 递归处理子元素
  for (const child of node.childNodes) {
    const passHighlightStatusToChild = nodeWasHighlighted || isParentHighlighted;
    const domElement = buildDomTree(child, parentIframe, passHighlightStatusToChild);
    if (domElement) nodeData.children.push(domElement);
  }

  return nodeData;
}
```

### 2. **智能交互元素识别**

```javascript
function isInteractiveElement(element) {
  if (!element || element.nodeType !== Node.ELEMENT_NODE) {
    return false;
  }

  const tagName = element.tagName.toLowerCase();
  const role = element.getAttribute('role');
  const ariaRole = element.getAttribute('aria-role');

  // 1. 基础交互元素
  const interactiveElements = new Set([
    'a', 'button', 'input', 'select', 'textarea', 'details', 'summary', 'label'
  ]);

  // 2. 交互角色
  const interactiveRoles = new Set([
    'button', 'link', 'menuitem', 'option', 'radio', 'checkbox', 'tab', 'switch'
  ]);

  // 3. 基础角色/属性检查
  const hasInteractiveRole = 
    interactiveElements.has(tagName) || 
    interactiveRoles.has(role) || 
    interactiveRoles.has(ariaRole);

  if (hasInteractiveRole) return true;

  // 4. 事件监听器检查
  try {
    if (typeof getEventListeners === 'function') {
      const listeners = getEventListeners(element);
      const mouseEvents = ['click', 'mousedown', 'mouseup', 'dblclick'];
      for (const eventType of mouseEvents) {
        if (listeners[eventType] && listeners[eventType].length > 0) {
          return true;
        }
      }
    }
  } catch (e) {
    // getEventListeners可能不可用
  }

  // 5. 样式检查
  const computedStyle = getCachedComputedStyle(element);
  const cursor = computedStyle.cursor;
  
  // 指针样式表示可交互
  if (cursor === 'pointer' || cursor === 'hand') {
    return true;
  }

  // 6. 属性检查
  const hasInteractiveAttributes = 
    element.hasAttribute('onclick') ||
    element.hasAttribute('onmousedown') ||
    element.hasAttribute('onmouseup') ||
    element.hasAttribute('tabindex') ||
    element.getAttribute('contenteditable') === 'true';

  return hasInteractiveAttributes;
}
```

## 🎯 关键技术特点

### 1. **性能优化策略**
- **缓存机制**: getBoundingClientRect和getComputedStyle结果缓存
- **视口裁剪**: 只处理视口内及扩展区域的元素
- **批量操作**: DOM操作批量执行，减少重排重绘
- **异步处理**: 使用Promise和async/await优化执行流程

### 2. **错误处理机制**
- **多层错误捕获**: Action、Page、DOM各层都有错误处理
- **优雅降级**: 操作失败时提供备选策略
- **详细错误信息**: 包含上下文信息的错误消息
- **重试机制**: 关键操作支持自动重试

### 3. **兼容性处理**
- **多种输入方式**: 支持不同类型的表单元素
- **事件触发**: 确保触发正确的DOM事件
- **iframe支持**: 处理跨frame的元素操作
- **Shadow DOM**: 支持现代Web组件

这套工具实现展现了nanobrowser在浏览器自动化方面的深厚技术功底，通过精心设计的分层架构和智能化的操作策略，实现了既强大又可靠的表单填写和DOM操作能力。

---

# AI决策与处理流程深度解析

## 🧠 AI在nanobrowser中的核心作用

AI不仅仅是一个"工具调用者"，而是整个系统的**智能决策中心**，负责理解任务、分析状态、制定策略、执行操作。

## 🔄 完整的AI处理流程

### 1. **AI决策流程总览**

```mermaid
flowchart TD
    A[用户任务输入] --> B[Executor初始化]
    B --> C{需要规划?}
    C -->|是| D[PlannerAgent分析]
    C -->|否| E[NavigatorAgent执行]

    D --> F[任务分解与策略制定]
    F --> G[更新消息历史]
    G --> E

    E --> H[获取浏览器状态]
    H --> I[构建Prompt]
    I --> J[LLM推理决策]
    J --> K[解析结构化输出]
    K --> L[生成Action序列]
    L --> M[执行Actions]
    M --> N[收集执行结果]
    N --> O{任务完成?}

    O -->|否| P{需要验证?}
    O -->|是| Q[ValidatorAgent验证]

    P -->|否| E
    P -->|是| Q

    Q --> R[验证结果正确性]
    R --> S{验证通过?}
    S -->|是| T[任务完成]
    S -->|否| U[重置状态继续]
    U --> E

    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style E fill:#fff3e0
    style J fill:#ffeb3b
    style Q fill:#ffebee
    style T fill:#e8f5e8
```

### 2. **AI状态理解机制**

#### **浏览器状态转换为AI可理解的格式**

```typescript
// BasePrompt.buildBrowserStateUserMessage()
async buildBrowserStateUserMessage(context: AgentContext): Promise<HumanMessage> {
  // 1. 获取完整浏览器状态
  const browserState = await context.browserContext.getState(context.options.useVision);

  // 2. 提取可交互元素
  const rawElementsText = browserState.elementTree.clickableElementsToString(context.options.includeAttributes);

  // 3. 构建滚动信息
  const scrollInfo = `[Scroll info] window.scrollY: ${browserState.scrollY}, document.body.scrollHeight: ${browserState.scrollHeight}, visual viewport height: ${browserState.visualViewportHeight}`;

  // 4. 格式化元素信息
  const elementsText = wrapUntrustedContent(rawElementsText);
  const formattedElementsText = `${scrollInfo}\n[Start of page]\n${elementsText}\n[End of page]\n`;

  // 5. 构建标签页信息
  const currentTab = `{id: ${browserState.tabId}, url: ${browserState.url}, title: ${browserState.title}}`;
  const otherTabs = browserState.tabs.filter(tab => tab.id !== browserState.tabId);

  // 6. 组装完整状态描述
  const stateDescription = `
[Task history memory ends]
[Current state starts here]
Current tab: ${currentTab}
Other available tabs: ${otherTabs.map(tab => `- {id: ${tab.id}, url: ${tab.url}, title: ${tab.title}}`).join('\n')}
Interactive elements from top layer of the current page inside the viewport:
${formattedElementsText}
${stepInfoDescription}
${actionResultsDescription}
`;

  // 7. 支持视觉输入
  if (browserState.screenshot && context.options.useVision) {
    return new HumanMessage({
      content: [
        { type: 'text', text: stateDescription },
        { type: 'image_url', image_url: { url: `data:image/jpeg;base64,${browserState.screenshot}` } },
      ],
    });
  }

  return new HumanMessage(stateDescription);
}
```

#### **AI看到的页面状态示例**

```
[Task history memory ends]
[Current state starts here]
Current tab: {id: 123, url: "https://amazon.com", title: "Amazon.com"}
Other available tabs:
  - {id: 124, url: "https://google.com", title: "Google"}
Interactive elements from top layer of the current page inside the viewport:
[Scroll info] window.scrollY: 0, document.body.scrollHeight: 2400, visual viewport height: 800
[Start of page]
<click>0 "Amazon" <select>1 "All Departments" <input>2 "Search Amazon" <click>3 "Search" <click>4 "Account & Lists" <click>5 "Returns & Orders" <click>6 "Cart"
[End of page]
Step 1 of 100
Previous action results:
- Navigated to Amazon homepage successfully
```

### 3. **LLM推理决策核心机制**

#### **结构化输出处理**

```typescript
// NavigatorAgent.invoke()
async invoke(inputMessages: BaseMessage[]): Promise<NavigatorResult> {
  // 1. 优先使用结构化输出
  if (this.withStructuredOutput) {
    try {
      return await this.invokeWithJsonParser(inputMessages);
    } catch (error) {
      logger.error('JsonOutputParser failed, falling back to manual extraction');
      // 回退到手动解析
    }
  }

  // 2. 手动JSON提取
  const response = await this.chatLLM.invoke(inputMessages);
  const extractedJson = extractJsonFromModelOutput(response.content);
  return this.validateModelOutput(extractedJson);
}

// 结构化输出实现
private async invokeWithJsonParser(inputMessages: BaseMessage[]): Promise<NavigatorResult> {
  // 1. 创建JSON输出解析器
  const parser = new JsonOutputParser();

  // 2. 构建包含Schema的系统消息
  const schemaDescription = this.getJsonSchemaDescription();
  const systemMessage = new SystemMessage(`You must respond with valid JSON that matches the expected schema.

Expected JSON schema:
${schemaDescription}

Important rules:
- Respond with valid JSON only
- Do not include any markdown formatting or code blocks
- Ensure all required fields are present
- Follow the exact field names and types specified`);

  // 3. 创建处理链：LLM → JSON解析器
  const chain = this.chatLLM.pipe(parser);

  // 4. 执行推理
  const result = await chain.invoke([systemMessage, ...inputMessages]);
  return this.validateModelOutput(result);
}
```

#### **AI输出Schema定义**

```typescript
// NavigatorAgent的输出格式
const navigatorOutputSchema = z.object({
  current_state: z.object({
    evaluation_previous_goal: z.string().describe("Success|Failed|Unknown - 分析前一个目标是否成功"),
    memory: z.string().describe("已完成的工作和需要记住的信息"),
    next_goal: z.string().describe("下一个即时行动的目标"),
  }),
  action: z.array(actionSchema).describe("要执行的动作序列"),
});

// 动态生成的Action Schema
function buildDynamicActionSchema(actions: Action[]): z.ZodType {
  let schema = z.object({});
  for (const action of actions) {
    schema = schema.extend({
      [action.name()]: action.schema.schema.nullable().optional()
    });
  }
  return schema;
}
```

### 4. **AI决策示例分析**

#### **电商购物任务的AI推理过程**

**输入状态**：
```
Current tab: {id: 123, url: "https://amazon.com", title: "Amazon"}
Interactive elements:
<input>2 "Search Amazon" <click>3 "Search" <select>1 "All Departments"
Task: 找一个500美元以下的无线耳机，要求降噪功能好，评分4星以上
```

**AI推理输出**：
```json
{
  "current_state": {
    "evaluation_previous_goal": "Success - 已成功导航到Amazon主页",
    "memory": "任务：寻找500美元以下的无线降噪耳机，评分4星以上。当前在Amazon主页，需要搜索产品。",
    "next_goal": "在搜索框中输入'wireless noise cancelling headphones'进行产品搜索"
  },
  "action": [
    {
      "input_text": {
        "index": 2,
        "text": "wireless noise cancelling headphones",
        "intent": "搜索无线降噪耳机"
      }
    },
    {
      "click_element": {
        "index": 3,
        "intent": "点击搜索按钮执行搜索"
      }
    }
  ]
}
```

#### **复杂表单填写的AI推理**

**输入状态**：
```
Interactive elements:
<input>5 "First Name" <input>6 "Last Name" <input>7 "Email" <select>8 "Country" <input>9 "Phone"
Task: 填写求职申请表，使用我的个人信息
```

**AI推理输出**：
```json
{
  "current_state": {
    "evaluation_previous_goal": "Success - 已定位到求职申请表单",
    "memory": "需要填写求职申请表，包含姓名、邮箱、国家、电话等字段。",
    "next_goal": "逐步填写表单的各个字段"
  },
  "action": [
    {
      "input_text": {
        "index": 5,
        "text": "John",
        "intent": "填写名字"
      }
    },
    {
      "input_text": {
        "index": 6,
        "text": "Smith",
        "intent": "填写姓氏"
      }
    },
    {
      "input_text": {
        "index": 7,
        "text": "<EMAIL>",
        "intent": "填写邮箱地址"
      }
    },
    {
      "get_dropdown_options": {
        "index": 8,
        "intent": "获取国家选项列表"
      }
    }
  ]
}
```

### 5. **智能错误处理与适应**

#### **DOM变化检测与适应**

```typescript
// NavigatorAgent.doMultiAction()
async doMultiAction(actions: any[]): Promise<ActionResult[]> {
  const results: ActionResult[] = [];
  let cachedPathHashes: Set<string>;

  for (const [i, action] of actions.entries()) {
    // 1. 检查DOM是否发生变化
    const indexArg = actionInstance.getIndexArg(actionArgs);
    if (i > 0 && indexArg !== null) {
      const newState = await browserContext.getState();
      const newPathHashes = await calcBranchPathHashSet(newState);

      // 2. 如果DOM结构变化，停止执行
      if (!newPathHashes.isSubsetOf(cachedPathHashes)) {
        const msg = `Something new appeared after action ${i} / ${actions.length}`;
        results.push(new ActionResult({
          extractedContent: msg,
          includeInMemory: true,
        }));
        break; // 让AI重新分析新状态
      }
    }

    // 3. 执行Action
    const result = await actionInstance.call(actionArgs);
    results.push(result);
  }

  return results;
}
```

#### **智能重试与错误恢复**

```typescript
// Executor.navigate()
private async navigate(): Promise<boolean> {
  try {
    const navOutput = await this.navigator.execute();
    context.consecutiveFailures = 0; // 重置失败计数

    if (navOutput.result?.done) {
      return true; // 任务完成
    }
  } catch (error) {
    context.consecutiveFailures++;

    // 连续失败超过阈值，停止执行
    if (context.consecutiveFailures >= maxConsecutiveFailures) {
      throw new Error('Too many consecutive failures');
    }

    // 记录错误但继续尝试
    logger.error('Navigation failed, will retry:', error);
  }

  return false;
}
```

### 6. **AI记忆与上下文管理**

#### **消息历史管理**

```typescript
// MessageManager处理AI的记忆
export class MessageManager {
  private messages: BaseMessage[] = [];

  // 添加AI输出到记忆
  addModelOutput(output: any): void {
    const content = `Navigator output: ${JSON.stringify(output)}`;
    this.messages.push(new AIMessage(content));
  }

  // 添加Action结果到记忆
  addActionResults(results: ActionResult[]): void {
    const content = results
      .filter(r => r.includeInMemory)
      .map(r => r.extractedContent || r.error)
      .join('\n');
    this.messages.push(new HumanMessage(`Action results: ${content}`));
  }

  // 智能Token管理
  getMessages(): BaseMessage[] {
    // 保持在Token限制内，移除最旧的消息
    while (this.estimateTokens() > this.maxTokens) {
      this.messages.shift();
    }
    return this.messages;
  }
}
```

#### **状态持久化与重放**

```typescript
// 历史重放机制
async executeHistoryStep(historyItem: AgentStepRecord): Promise<ActionResult[]> {
  // 1. 解析历史记录
  const parsedOutput = JSON.parse(historyItem.modelOutput);
  const actionsToReplay = parsedOutput.action;

  // 2. 更新Action索引（适应DOM变化）
  const updatedActions = await this.updateActionIndices(
    historyItem.historyElements,
    actionsToReplay,
    currentState
  );

  // 3. 执行更新后的Actions
  return await this.doMultiAction(updatedActions);
}
```

## 🎯 AI处理的关键特点

### 1. **多模态理解**
- **文本分析**: 解析DOM结构和元素属性
- **视觉理解**: 支持截图输入进行视觉推理
- **上下文记忆**: 维护任务执行的完整历史

### 2. **智能决策**
- **状态评估**: 分析当前页面状态和任务进度
- **策略制定**: 根据目标制定多步骤执行计划
- **动态适应**: 根据执行结果调整后续策略

### 3. **结构化输出**
- **Schema验证**: 确保AI输出符合预定格式
- **类型安全**: 通过Zod进行运行时类型检查
- **错误恢复**: 多重解析策略确保输出可用

### 4. **上下文管理**
- **记忆机制**: 维护任务执行的完整上下文
- **Token优化**: 智能管理对话历史长度
- **状态同步**: 确保AI决策基于最新状态

这套AI处理机制展现了nanobrowser在智能决策方面的深度思考，通过精心设计的提示工程、结构化输出和上下文管理，实现了既智能又可靠的浏览器自动化能力。
