# nanobrowser Agent解耦集成设计方案

## 🎯 解耦集成策略

基于对现有架构的深入分析，我设计了一个**渐进式解耦集成方案**，既保持现有功能的稳定性，又为未来的扩展奠定基础。

## 🏗️ 核心设计原则

### 1. **适配器模式** - 兼容现有Agent
### 2. **策略模式** - 灵活的执行策略
### 3. **工厂模式** - 动态Agent创建
### 4. **观察者模式** - 松耦合通信
### 5. **插件模式** - 第三方扩展支持

## 🔧 解耦集成架构

```typescript
┌─────────────────────────────────────────────────────────────┐
│                  Agent Orchestrator                         │
│  ┌─────────────────────────────────────────────────────────┤
│  │              Agent Registry                             │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  │   Legacy    │  │   Modern    │  │   Plugin    │     │
│  │  │  Adapters   │  │   Agents    │  │   Agents    │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │
│  └─────────────────────────────────────────────────────────┤
│  │              Strategy Engine                            │
│  │  • Task Analysis  • Agent Selection  • Flow Control    │
│  └─────────────────────────────────────────────────────────┤
│  │              Event Bus                                  │
│  │  • Agent Communication  • State Sync  • Monitoring     │
│  └─────────────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────────────┘
```

## 🎭 Agent抽象层设计

### 1. **统一Agent接口**

```typescript
// 新的统一Agent接口
export interface IAgent {
  readonly id: string;
  readonly capabilities: AgentCapability[];
  readonly priority: number;
  
  // 核心方法
  canHandle(task: TaskDescriptor): Promise<boolean>;
  execute(context: ExecutionContext): Promise<AgentResult>;
  
  // 生命周期
  initialize(config: AgentConfig): Promise<void>;
  cleanup(): Promise<void>;
  
  // 状态管理
  getState(): AgentState;
  setState(state: Partial<AgentState>): void;
}

// Agent能力声明
export interface AgentCapability {
  type: 'navigation' | 'planning' | 'validation' | 'extraction' | 'analysis';
  domains: string[];  // 适用的网站域名
  complexity: 'simple' | 'medium' | 'complex';
  requirements: string[];  // 依赖的工具或服务
}

// 任务描述符
export interface TaskDescriptor {
  type: TaskType;
  complexity: number;  // 0-1
  domain: string;
  requirements: string[];
  context: Record<string, any>;
}
```

### 2. **Legacy Agent适配器**

```typescript
// 包装现有的BaseAgent
export class LegacyAgentAdapter implements IAgent {
  private legacyAgent: BaseAgent<any, any>;
  
  constructor(
    private agentClass: new (...args: any[]) => BaseAgent<any, any>,
    private config: LegacyAgentConfig
  ) {}
  
  async canHandle(task: TaskDescriptor): Promise<boolean> {
    // 基于Agent类型和任务类型的匹配逻辑
    if (this.agentClass === NavigatorAgent) {
      return task.type === 'navigation' || task.type === 'interaction';
    }
    if (this.agentClass === PlannerAgent) {
      return task.type === 'planning' || task.complexity > 0.7;
    }
    if (this.agentClass === ValidatorAgent) {
      return task.type === 'validation';
    }
    return false;
  }
  
  async execute(context: ExecutionContext): Promise<AgentResult> {
    // 转换新的ExecutionContext到旧的AgentContext
    const legacyContext = this.convertToLegacyContext(context);
    
    // 执行原有的Agent逻辑
    const result = await this.legacyAgent.execute();
    
    // 转换结果格式
    return this.convertToAgentResult(result);
  }
  
  private convertToLegacyContext(context: ExecutionContext): AgentContext {
    // 实现新旧上下文的转换逻辑
    return new AgentContext(
      context.taskId,
      context.browserContext,
      context.messageManager,
      context.eventManager,
      context.options
    );
  }
}
```

### 3. **现代Agent实现**

```typescript
// 基于LangGraph的现代Agent
export class ModernNavigatorAgent implements IAgent {
  readonly id = 'modern-navigator';
  readonly capabilities: AgentCapability[] = [
    {
      type: 'navigation',
      domains: ['*'],
      complexity: 'complex',
      requirements: ['stagehand', 'computer-use']
    }
  ];
  
  private stagehand: Stagehand;
  private workflow: StateGraph;
  
  async canHandle(task: TaskDescriptor): Promise<boolean> {
    return task.type === 'navigation' && task.complexity > 0.5;
  }
  
  async execute(context: ExecutionContext): Promise<AgentResult> {
    // 使用LangGraph + Stagehand的现代实现
    const state = {
      task: context.task,
      browserState: await context.browserContext.getState(),
      messages: context.messages,
    };
    
    const result = await this.workflow.invoke(state);
    
    return {
      success: true,
      data: result,
      metadata: {
        agent: this.id,
        duration: Date.now() - context.startTime,
      }
    };
  }
  
  private buildWorkflow(): StateGraph {
    return new StateGraph(AgentState)
      .addNode("analyze", this.analyzeTask)
      .addNode("navigate", this.navigateWithStagehand)
      .addNode("validate", this.validateResult)
      .addConditionalEdges("analyze", this.routeBasedOnComplexity)
      .compile();
  }
}
```

## 🎯 场景化Agent设计

### 1. **基础场景Agent**

```typescript
// 表单填写专家
export class FormFillerAgent implements IAgent {
  readonly capabilities = [{
    type: 'interaction',
    domains: ['*'],
    complexity: 'simple',
    requirements: ['form-detection']
  }];
  
  async canHandle(task: TaskDescriptor): Promise<boolean> {
    return task.type === 'form_filling' || 
           task.context.hasFormElements === true;
  }
  
  async execute(context: ExecutionContext): Promise<AgentResult> {
    const page = context.stagehand.page;
    
    // 智能表单检测和填写
    const forms = await page.extract({
      instruction: "find all forms and their required fields",
      schema: z.object({
        forms: z.array(z.object({
          selector: z.string(),
          fields: z.array(z.object({
            name: z.string(),
            type: z.string(),
            required: z.boolean(),
          }))
        }))
      })
    });
    
    // 执行填写逻辑
    for (const form of forms.forms) {
      await this.fillForm(page, form, context.task.data);
    }
    
    return { success: true, data: { formsProcessed: forms.forms.length } };
  }
}

// 数据提取专家
export class DataExtractorAgent implements IAgent {
  readonly capabilities = [{
    type: 'extraction',
    domains: ['*'],
    complexity: 'medium',
    requirements: ['ai-extraction']
  }];
  
  async execute(context: ExecutionContext): Promise<AgentResult> {
    const page = context.stagehand.page;
    const { schema, instruction } = context.task.extractionConfig;
    
    // 使用Stagehand的AI提取能力
    const data = await page.extract({
      instruction,
      schema: z.object(schema)
    });
    
    return { success: true, data };
  }
}
```

### 2. **业务场景Agent**

```typescript
// 电商购物Agent
export class EcommerceAgent implements IAgent {
  readonly capabilities = [{
    type: 'navigation',
    domains: ['amazon.com', 'taobao.com', '*.shop', '*.store'],
    complexity: 'complex',
    requirements: ['payment-handling', 'cart-management']
  }];
  
  async execute(context: ExecutionContext): Promise<AgentResult> {
    const workflow = new StateGraph(EcommerceState)
      .addNode("search", this.searchProduct)
      .addNode("compare", this.compareProducts)
      .addNode("addToCart", this.addToCart)
      .addNode("checkout", this.checkout)
      .addConditionalEdges("search", this.routeAfterSearch)
      .compile();
    
    return await workflow.invoke({
      query: context.task.searchQuery,
      budget: context.task.budget,
      preferences: context.task.preferences,
    });
  }
  
  private async searchProduct(state: EcommerceState) {
    const page = this.stagehand.page;
    await page.act(`search for ${state.query}`);
    
    const products = await page.extract({
      instruction: "extract product information from search results",
      schema: productListSchema
    });
    
    return { products };
  }
}

// 社交媒体管理Agent
export class SocialMediaAgent implements IAgent {
  readonly capabilities = [{
    type: 'interaction',
    domains: ['twitter.com', 'linkedin.com', 'facebook.com'],
    complexity: 'medium',
    requirements: ['content-generation', 'scheduling']
  }];
  
  async execute(context: ExecutionContext): Promise<AgentResult> {
    const { platform, action, content } = context.task;
    
    switch (action) {
      case 'post':
        return await this.createPost(platform, content);
      case 'schedule':
        return await this.schedulePost(platform, content);
      case 'analyze':
        return await this.analyzeEngagement(platform);
      default:
        throw new Error(`Unsupported action: ${action}`);
    }
  }
}
```

### 3. **专业化Agent**

```typescript
// 测试自动化Agent
export class TestAutomationAgent implements IAgent {
  readonly capabilities = [{
    type: 'validation',
    domains: ['*'],
    complexity: 'complex',
    requirements: ['test-framework', 'assertion-library']
  }];
  
  async execute(context: ExecutionContext): Promise<AgentResult> {
    const { testSuite, environment } = context.task;
    
    const results = [];
    for (const testCase of testSuite.tests) {
      const result = await this.executeTestCase(testCase, environment);
      results.push(result);
    }
    
    return {
      success: results.every(r => r.passed),
      data: {
        total: results.length,
        passed: results.filter(r => r.passed).length,
        failed: results.filter(r => !r.passed).length,
        details: results
      }
    };
  }
}

// 性能监控Agent
export class PerformanceMonitorAgent implements IAgent {
  readonly capabilities = [{
    type: 'analysis',
    domains: ['*'],
    complexity: 'medium',
    requirements: ['performance-api', 'metrics-collection']
  }];
  
  async execute(context: ExecutionContext): Promise<AgentResult> {
    const page = context.stagehand.page;
    
    // 收集性能指标
    const metrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      const paint = performance.getEntriesByType('paint');
      
      return {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime,
        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime,
      };
    });
    
    return { success: true, data: metrics };
  }
}
```

## 🔧 扩展机制设计

### 1. **Agent注册系统**

```typescript
export class AgentRegistry {
  private agents: Map<string, IAgent> = new Map();
  private capabilities: Map<string, AgentCapability[]> = new Map();
  
  register(agent: IAgent): void {
    this.agents.set(agent.id, agent);
    this.capabilities.set(agent.id, agent.capabilities);
    
    logger.info(`Agent registered: ${agent.id}`, {
      capabilities: agent.capabilities.length,
      priority: agent.priority
    });
  }
  
  unregister(agentId: string): void {
    this.agents.delete(agentId);
    this.capabilities.delete(agentId);
  }
  
  findSuitableAgents(task: TaskDescriptor): IAgent[] {
    const candidates = [];
    
    for (const [id, agent] of this.agents) {
      if (await agent.canHandle(task)) {
        candidates.push(agent);
      }
    }
    
    // 按优先级和能力匹配度排序
    return candidates.sort((a, b) => {
      const scoreA = this.calculateMatchScore(a, task);
      const scoreB = this.calculateMatchScore(b, task);
      return scoreB - scoreA;
    });
  }
  
  private calculateMatchScore(agent: IAgent, task: TaskDescriptor): number {
    let score = agent.priority;
    
    // 能力匹配度
    const matchingCapabilities = agent.capabilities.filter(cap => 
      cap.type === task.type || 
      cap.domains.includes(task.domain) ||
      cap.domains.includes('*')
    );
    
    score += matchingCapabilities.length * 10;
    
    // 复杂度匹配
    const complexityMatch = agent.capabilities.some(cap => 
      (cap.complexity === 'simple' && task.complexity < 0.3) ||
      (cap.complexity === 'medium' && task.complexity < 0.7) ||
      (cap.complexity === 'complex')
    );
    
    if (complexityMatch) score += 5;
    
    return score;
  }
}
```

### 2. **插件系统**

```typescript
export interface AgentPlugin {
  name: string;
  version: string;
  description: string;
  
  // 插件提供的Agent
  agents: (new (...args: any[]) => IAgent)[];
  
  // 插件依赖
  dependencies: string[];
  
  // 插件配置
  config?: Record<string, any>;
  
  // 生命周期钩子
  onLoad?(registry: AgentRegistry): Promise<void>;
  onUnload?(): Promise<void>;
}

export class PluginManager {
  private plugins: Map<string, AgentPlugin> = new Map();
  private registry: AgentRegistry;
  
  constructor(registry: AgentRegistry) {
    this.registry = registry;
  }
  
  async loadPlugin(plugin: AgentPlugin): Promise<void> {
    // 检查依赖
    for (const dep of plugin.dependencies) {
      if (!this.plugins.has(dep)) {
        throw new Error(`Missing dependency: ${dep}`);
      }
    }
    
    // 注册Agent
    for (const AgentClass of plugin.agents) {
      const agent = new AgentClass(plugin.config);
      await agent.initialize(plugin.config);
      this.registry.register(agent);
    }
    
    // 执行加载钩子
    if (plugin.onLoad) {
      await plugin.onLoad(this.registry);
    }
    
    this.plugins.set(plugin.name, plugin);
    logger.info(`Plugin loaded: ${plugin.name} v${plugin.version}`);
  }
  
  async unloadPlugin(name: string): Promise<void> {
    const plugin = this.plugins.get(name);
    if (!plugin) return;
    
    // 执行卸载钩子
    if (plugin.onUnload) {
      await plugin.onUnload();
    }
    
    // 注销Agent
    for (const AgentClass of plugin.agents) {
      // 找到并注销对应的Agent实例
      // 这里需要维护Agent类到实例的映射
    }
    
    this.plugins.delete(name);
  }
}
```

### 3. **策略引擎**

```typescript
export class StrategyEngine {
  private strategies: Map<string, ExecutionStrategy> = new Map();
  
  constructor(private registry: AgentRegistry) {
    this.initializeDefaultStrategies();
  }
  
  async selectStrategy(task: TaskDescriptor): Promise<ExecutionStrategy> {
    // 基于任务特征选择执行策略
    if (task.complexity > 0.8) {
      return this.strategies.get('complex-task-strategy')!;
    }
    
    if (task.type === 'extraction') {
      return this.strategies.get('data-extraction-strategy')!;
    }
    
    if (task.domain.includes('ecommerce')) {
      return this.strategies.get('ecommerce-strategy')!;
    }
    
    return this.strategies.get('default-strategy')!;
  }
  
  private initializeDefaultStrategies(): void {
    // 默认策略：单Agent执行
    this.strategies.set('default-strategy', {
      name: 'default',
      execute: async (task, context) => {
        const agents = this.registry.findSuitableAgents(task);
        if (agents.length === 0) {
          throw new Error('No suitable agent found');
        }
        
        return await agents[0].execute(context);
      }
    });
    
    // 复杂任务策略：多Agent协作
    this.strategies.set('complex-task-strategy', {
      name: 'complex-task',
      execute: async (task, context) => {
        // 分解任务
        const subtasks = await this.decomposeTask(task);
        
        // 并行或串行执行
        const results = [];
        for (const subtask of subtasks) {
          const agents = this.registry.findSuitableAgents(subtask);
          const result = await agents[0].execute({
            ...context,
            task: subtask
          });
          results.push(result);
        }
        
        // 合并结果
        return this.mergeResults(results);
      }
    });
  }
}
```

## 🎯 集成实施路径

### Phase 1: 适配器层实现 (1-2周)
1. 实现IAgent接口
2. 创建LegacyAgentAdapter
3. 基础的AgentRegistry

### Phase 2: 现代Agent开发 (2-3周)
1. 基于LangGraph的ModernAgent
2. Stagehand集成
3. 场景化Agent实现

### Phase 3: 扩展系统 (2-3周)
1. 插件系统实现
2. 策略引擎开发
3. 监控和调试工具

### Phase 4: 生产优化 (1-2周)
1. 性能优化
2. 错误处理增强
3. 文档和示例

这个设计方案实现了**完全的向后兼容**，同时为未来的扩展提供了**无限的可能性**。你觉得这个方案如何？有什么特定的场景或技术细节想深入讨论吗？
