# nanobrowser AI上下文处理与浏览器集成深度分析

## 🧠 AI上下文管理系统

### 1. **上下文管理架构总览**

```mermaid
graph TB
    subgraph "AI上下文管理层"
        A[AgentContext] --> B[MessageManager]
        A --> C[EventManager]
        A --> D[BrowserContext]
        A --> E[AgentStepHistory]
    end
    
    subgraph "消息管理系统"
        B --> F[MessageHistory]
        F --> G[ManagedMessage]
        G --> H[BaseMessage + Metadata]
    end
    
    subgraph "浏览器集成层"
        D --> I[Page Manager]
        I --> J[Puppeteer Core]
        J --> K[Chrome DevTools Protocol]
        K --> L[浏览器实例]
    end
    
    subgraph "历史与状态"
        E --> M[AgentStepRecord]
        M --> N[ModelOutput + ActionResults + BrowserState]
    end
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style D fill:#fff3e0
    style E fill:#ffebee
```

### 2. **AgentContext - 核心上下文管理器**

```typescript
export class AgentContext {
  // 核心组件
  controller: AbortController;           // 任务取消控制
  taskId: string;                       // 任务唯一标识
  browserContext: BrowserContext;       // 浏览器上下文
  messageManager: MessageManager;       // 消息历史管理
  eventManager: EventManager;           // 事件管理
  options: AgentOptions;                // 配置选项
  
  // 执行状态
  paused: boolean;                      // 暂停状态
  stopped: boolean;                     // 停止状态
  nSteps: number;                       // 执行步数
  consecutiveFailures: number;          // 连续失败次数
  actionResults: ActionResult[];        // 动作执行结果
  stateMessageAdded: boolean;           // 状态消息标记
  history: AgentStepHistory;            // 执行历史
  
  // 事件发射
  async emitEvent(actor: Actors, state: ExecutionState, eventDetails: string) {
    const event = new AgentEvent(actor, state, {
      taskId: this.taskId,
      step: this.nSteps,
      maxSteps: this.options.maxSteps,
      details: eventDetails,
    });
    await this.eventManager.emit(event);
  }
  
  // 控制方法
  async pause() { this.paused = true; }
  async resume() { this.paused = false; }
  async stop() { 
    this.stopped = true;
    setTimeout(() => this.controller.abort(), 300);
  }
}
```

## 💾 消息管理系统深度解析

### 1. **MessageManager - AI记忆的核心**

```typescript
export class MessageManager {
  private history: MessageHistory;
  private settings: MessageManagerSettings;
  private toolId: number = 0;

  // 初始化任务消息
  public initTaskMessages(systemMessage: SystemMessage, task: string, context?: string): void {
    // 1. 添加系统消息
    this.addMessageWithTokens(systemMessage, 'init');
    
    // 2. 添加任务描述
    const wrappedTask = wrapUserRequest(task);
    const taskMessage = new HumanMessage({ content: wrappedTask });
    this.addMessageWithTokens(taskMessage, 'init');
    
    // 3. 添加示例工具调用
    const exampleToolCall = new AIMessage({
      content: 'tool call',
      tool_calls: [{
        name: 'AgentOutput',
        args: { message: 'Browser started' },
        id: String(this.nextToolId()),
        type: 'tool_call' as const,
      }],
    });
    this.addMessageWithTokens(exampleToolCall, 'init');
    
    // 4. 添加历史开始标记
    const historyStartMessage = new HumanMessage({
      content: '[Your task history memory starts here]',
    });
    this.addMessageWithTokens(historyStartMessage);
  }

  // 添加新任务
  public addNewTask(newTask: string): void {
    const content = `Your new ultimate task is: """${newTask}""". This is a follow-up of the previous tasks. Make sure to take all of the previous context into account and finish your new ultimate task.`;
    const wrappedContent = wrapUserRequest(content);
    const msg = new HumanMessage({ content: wrappedContent });
    this.addMessageWithTokens(msg);
  }

  // 添加AI输出到记忆
  public addModelOutput(modelOutput: Record<string, any>): void {
    const toolCallId = this.nextToolId();
    const toolCalls = [{
      name: 'AgentOutput',
      args: modelOutput,
      id: String(toolCallId),
      type: 'tool_call' as const,
    }];

    const msg = new AIMessage({
      content: 'tool call',
      tool_calls: toolCalls,
    });
    this.addMessageWithTokens(msg);
    
    // 添加工具响应占位符
    this.addToolMessage('tool call response', toolCallId);
  }

  // 智能Token管理
  public getMessages(): BaseMessage[] {
    // 在Token限制内裁剪消息
    this.trimMessagesToFitTokenLimit();
    return this.history.messages.map(m => m.message);
  }
}
```

### 2. **智能Token管理机制**

```typescript
// Token估算和管理
private estimateTokens(message: BaseMessage): number {
  let tokens = 0;
  
  if (typeof message.content === 'string') {
    // 文本内容：字符数 / 每Token字符数
    tokens += Math.ceil(message.content.length / this.settings.estimatedCharactersPerToken);
  } else if (Array.isArray(message.content)) {
    // 多模态内容
    for (const item of message.content) {
      if (item.type === 'text') {
        tokens += Math.ceil(item.text.length / this.settings.estimatedCharactersPerToken);
      } else if (item.type === 'image_url') {
        tokens += this.settings.imageTokens; // 图片固定Token数
      }
    }
  }
  
  return tokens;
}

private trimMessagesToFitTokenLimit(): void {
  while (this.history.totalTokens > this.settings.maxInputTokens && this.history.messages.length > 1) {
    // 保护初始化消息，从最旧的非初始化消息开始删除
    const firstNonInitIndex = this.history.messages.findIndex(m => m.metadata.type !== 'init');
    if (firstNonInitIndex !== -1) {
      this.history.removeMessage(firstNonInitIndex);
    } else {
      break; // 只剩初始化消息，停止删除
    }
  }
}
```

### 3. **消息类型和安全标签系统**

```typescript
// 安全标签包装
export function wrapUserRequest(content: string): string {
  return `<nano_user_request>\n${content}\n</nano_user_request>`;
}

export function wrapUntrustedContent(content: string): string {
  return `<nano_untrusted_content>\n${content}\n</nano_untrusted_content>`;
}

// 消息元数据
export class MessageMetadata {
  tokens: number;
  type: 'init' | 'task' | 'state' | 'output' | null;
  timestamp: number;
  
  constructor(tokens: number, type: 'init' | 'task' | 'state' | 'output' | null = null) {
    this.tokens = tokens;
    this.type = type;
    this.timestamp = Date.now();
  }
}
```

## 🌐 Puppeteer浏览器集成机制

### 1. **Chrome Extension + Puppeteer架构**

nanobrowser使用了独特的**Chrome Extension + Puppeteer**集成方式，这与传统的Playwright使用方式完全不同：

```typescript
// Page.attachPuppeteer() - 核心连接机制
async attachPuppeteer(): Promise<boolean> {
  if (!this._validWebPage) {
    return false;
  }

  logger.info('attaching puppeteer', this._tabId);
  
  // 1. 通过ExtensionTransport连接到Chrome标签页
  const browser = await connect({
    transport: await ExtensionTransport.connectTab(this._tabId),
    defaultViewport: null,
    protocol: 'cdp' as ProtocolType, // Chrome DevTools Protocol
  });
  this._browser = browser;

  // 2. 获取页面实例
  const [page] = await browser.pages();
  this._puppeteerPage = page;

  // 3. 添加反检测脚本
  await this._addAntiDetectionScripts();

  return true;
}
```

### 2. **反检测机制**

```typescript
private async _addAntiDetectionScripts(): Promise<void> {
  await this._puppeteerPage.evaluateOnNewDocument(`
    // 隐藏webdriver属性
    Object.defineProperty(navigator, 'webdriver', {
      get: () => undefined
    });

    // 模拟Chrome运行时
    window.chrome = { runtime: {} };

    // 权限查询拦截
    const originalQuery = window.navigator.permissions.query;
    window.navigator.permissions.query = (parameters) => (
      parameters.name === 'notifications' ?
        Promise.resolve({ state: Notification.permission }) :
        originalQuery(parameters)
    );

    // Shadow DOM开放模式
    const originalAttachShadow = Element.prototype.attachShadow;
    Element.prototype.attachShadow = function attachShadow(options) {
      return originalAttachShadow.call(this, { ...options, mode: "open" });
    };
  `);
}
```

### 3. **BrowserContext - 多标签页管理**

```typescript
export default class BrowserContext {
  private _currentTabId: number | null = null;
  private _attachedPages: Map<number, Page> = new Map();

  // 获取当前页面
  public async getCurrentPage(): Promise<Page> {
    if (!this._currentTabId) {
      // 1. 查询活动标签页
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (!tab?.id) {
        // 2. 创建新标签页
        const newTab = await chrome.tabs.create({ url: this._config.homePageUrl });
        activeTab = newTab;
      } else {
        activeTab = tab;
      }
      
      // 3. 创建并附加Page实例
      const page = await this._getOrCreatePage(activeTab);
      await this.attachPage(page);
      this._currentTabId = activeTab.id;
      return page;
    }
    
    return this._attachedPages.get(this._currentTabId)!;
  }

  // 切换标签页
  public async switchTab(tabId: number): Promise<Page> {
    // 1. 激活标签页
    await chrome.tabs.update(tabId, { active: true });
    await this.waitForTabEvents(tabId);

    // 2. 获取或创建Page实例
    const page = await this._getOrCreatePage(await chrome.tabs.get(tabId));
    await this.attachPage(page);
    this._currentTabId = tabId;
    return page;
  }

  // 打开新标签页
  public async openTab(url: string): Promise<Page> {
    // 1. URL安全检查
    if (!isUrlAllowed(url, this._config.allowedUrls, this._config.deniedUrls)) {
      throw new URLNotAllowedError(`URL: ${url} is not allowed`);
    }

    // 2. 创建标签页
    const tab = await chrome.tabs.create({ url, active: true });
    await this.waitForTabEvents(tab.id);

    // 3. 创建并附加Page
    const page = await this._getOrCreatePage(await chrome.tabs.get(tab.id));
    await this.attachPage(page);
    this._currentTabId = tab.id;
    return page;
  }
}
```

## 🔄 任务执行完整流程

### 1. **任务初始化流程**

```mermaid
sequenceDiagram
    participant User as 用户
    participant SP as SidePanel
    participant BG as Background Script
    participant Executor as Executor
    participant Context as AgentContext
    participant Browser as BrowserContext
    
    User->>SP: 输入任务
    SP->>BG: new_task消息
    BG->>Executor: 创建Executor实例
    Executor->>Context: 初始化AgentContext
    Context->>Browser: 创建BrowserContext
    Browser->>Browser: 获取当前标签页
    Browser->>Browser: 附加Puppeteer
    Context->>Context: 初始化MessageManager
    Context->>Context: 设置任务历史
    Executor->>Executor: 开始执行循环
```

### 2. **AI决策与执行循环**

```typescript
// Executor.execute() - 主执行循环
async execute(): Promise<void> {
  const context = this.context;
  context.nSteps = 0;
  let done = false;

  try {
    while (context.nSteps < this.context.options.maxSteps && !done) {
      // 1. 规划阶段 (每3步执行一次)
      if (context.nSteps % context.options.planningInterval === 0) {
        const planOutput = await this.planner.execute();
        if (planOutput.result) {
          const plan = { ...planOutput.result };
          context.messageManager.addPlan(JSON.stringify(plan));
        }
      }

      // 2. 导航执行阶段
      done = await this.navigate();
      
      // 3. 验证阶段 (任务完成时)
      if (done && context.options.validateOutput) {
        const validationOutput = await this.validator.execute();
        if (validationOutput.result && !validationOutput.result.is_valid) {
          done = false; // 验证失败，继续执行
          context.consecutiveValidatorFailures++;
        }
      }
    }
  } catch (error) {
    context.emitEvent(Actors.SYSTEM, ExecutionState.TASK_FAIL, error.message);
    throw error;
  }
}

// 导航执行
private async navigate(): Promise<boolean> {
  try {
    const navOutput = await this.navigator.execute();
    context.consecutiveFailures = 0; // 重置失败计数
    context.nSteps++;
    
    if (navOutput.result?.done) {
      return true; // 任务完成
    }
  } catch (error) {
    context.consecutiveFailures++;
    if (context.consecutiveFailures >= maxConsecutiveFailures) {
      throw new Error('Too many consecutive failures');
    }
  }
  
  return false;
}
```

### 3. **状态同步与历史记录**

```typescript
// NavigatorAgent.execute() - 状态管理
async execute(): Promise<AgentOutput<NavigatorResult>> {
  try {
    // 1. 添加当前状态到消息历史
    await this.addStateMessageToMemory();
    
    // 2. 获取消息历史并调用LLM
    const inputMessages = messageManager.getMessages();
    const modelOutput = await this.invoke(inputMessages);
    
    // 3. 移除状态消息，添加模型输出
    this.removeLastStateMessageFromMemory();
    this.addModelOutputToMemory(modelOutput);
    
    // 4. 执行Actions
    const actionResults = await this.doMultiAction(modelOutput.action);
    
    // 5. 记录到历史
    if (browserStateHistory) {
      const history = new AgentStepRecord(
        JSON.stringify(modelOutput),
        actionResults,
        browserStateHistory
      );
      this.context.history.history.push(history);
    }
    
    return agentOutput;
  } catch (error) {
    this.removeLastStateMessageFromMemory();
    throw error;
  }
}
```

## 🎯 关键技术特点

### 1. **Chrome Extension权限**
```json
{
  "permissions": [
    "storage",      // 本地存储
    "scripting",    // 脚本注入
    "tabs",         // 标签页管理
    "activeTab",    // 活动标签页访问
    "debugger",     // 调试器API (Puppeteer需要)
    "unlimitedStorage" // 无限存储
  ],
  "host_permissions": ["<all_urls>"] // 所有网站访问权限
}
```

### 2. **与Playwright的区别**
- **nanobrowser**: Chrome Extension + Puppeteer + Chrome DevTools Protocol
- **Playwright**: 独立浏览器实例 + 自有协议
- **优势**: 可以控制用户的真实浏览器会话，保持登录状态和Cookie
- **劣势**: 依赖Chrome Extension环境，部署复杂度较高

### 3. **上下文持久化**
```typescript
// 历史存储到Chrome Storage
const storeAgentStepHistory = async (sessionId: string, task: string, history: string): Promise<void> => {
  const storage = getSessionAgentStepHistoryStorage(sessionId);
  await storage.set({
    task,
    history,
    timestamp: getCurrentTimestamp(),
  });
};
```

这套AI上下文处理和浏览器集成机制展现了nanobrowser在系统设计方面的深度思考，通过精心设计的消息管理、智能的Token控制和创新的浏览器集成方式，实现了既智能又可靠的AI Agent系统。
