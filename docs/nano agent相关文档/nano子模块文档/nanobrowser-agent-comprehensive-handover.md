# nanobrowser Agent系统详细交接文档
## 基于Chrome Extension的智能浏览器自动化Agent系统

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**文档类型**: 技术交接文档  
**目标读者**: 开发工程师、系统架构师、产品经理  

---

## 📋 目录

1. [系统概述](#1-系统概述)
2. [核心架构设计](#2-核心架构设计)
3. [Agent模块详解](#3-agent模块详解)
4. [技术实现机制](#4-技术实现机制)
5. [关键流程分析](#5-关键流程分析)
6. [部署与配置](#6-部署与配置)
7. [性能与监控](#7-性能与监控)
8. [问题排查指南](#8-问题排查指南)
9. [扩展开发指南](#9-扩展开发指南)
10. [总结与展望](#10-总结与展望)

---

## 1. 系统概述

### 1.1 项目背景

nanobrowser是一个基于Chrome Extension的智能浏览器自动化系统，通过AI Agent技术实现复杂的Web任务自动化。该系统的核心创新在于：

- **真实浏览器环境**: 直接控制用户的Chrome浏览器，保持登录状态和Cookie
- **AI驱动决策**: 使用大语言模型(LLM)进行智能决策和任务规划
- **多Agent协作**: 采用规划-执行-验证的三Agent架构
- **Chrome Extension集成**: 通过浏览器扩展获得深度系统权限

### 1.2 系统定位

```mermaid
graph LR
    A[用户任务输入] --> B[nanobrowser Agent]
    B --> C[Chrome浏览器控制]
    C --> D[Web任务自动化]
    
    subgraph "竞品对比"
        E[Playwright - 独立浏览器]
        F[Selenium - WebDriver]
        G[Puppeteer - 无头浏览器]
    end
    
    B -.优势.-> H[保持用户状态]
    B -.优势.-> I[真实环境操作]
    B -.优势.-> J[AI智能决策]
    
    style B fill:#e1f5fe
    style H fill:#e8f5e8
    style I fill:#e8f5e8
    style J fill:#e8f5e8
```

### 1.3 核心特性

| 特性 | 描述 | 技术实现 |
|------|------|----------|
| **智能决策** | AI驱动的任务理解和执行策略制定 | LLM + Prompt Engineering |
| **真实环境** | 在用户真实浏览器中执行任务 | Chrome Extension + Puppeteer |
| **多模态理解** | 支持文本和视觉输入 | DOM分析 + 截图识别 |
| **状态保持** | 保持用户登录状态和浏览历史 | 真实浏览器会话 |
| **错误恢复** | 智能错误处理和任务重试 | 多层错误捕获机制 |
| **历史学习** | 从执行历史中学习和优化 | 历史重放 + 索引更新 |

### 1.4 应用场景

```mermaid
mindmap
  root((nanobrowser应用场景))
    电商自动化
      商品搜索
      价格比较
      订单处理
      库存监控
    数据采集
      网页爬取
      表单填写
      信息提取
      报告生成
    办公自动化
      邮件处理
      文档上传
      系统操作
      流程审批
    测试自动化
      功能测试
      回归测试
      用户体验测试
      兼容性测试
```

---

## 2. 核心架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "用户界面层"
        UI[SidePanel UI]
        POPUP[Popup Interface]
    end
    
    subgraph "Chrome Extension层"
        BG[Background Script]
        CS[Content Script]
        INJECT[Injected Script]
    end
    
    subgraph "Agent核心层"
        EXEC[Executor]
        PLAN[PlannerAgent]
        NAV[NavigatorAgent]
        VAL[ValidatorAgent]
    end
    
    subgraph "Action执行层"
        AB[ActionBuilder]
        ACTIONS[26个内置Actions]
        DOM[DOM操作引擎]
    end
    
    subgraph "浏览器控制层"
        BC[BrowserContext]
        PAGE[Page Manager]
        PUP[Puppeteer Core]
    end
    
    subgraph "AI服务层"
        LLM[LLM Provider]
        PROMPT[Prompt System]
        PARSE[Output Parser]
    end
    
    subgraph "数据管理层"
        MSG[MessageManager]
        HIST[History Manager]
        EVENT[Event Manager]
    end
    
    UI --> BG
    POPUP --> BG
    BG --> EXEC
    EXEC --> PLAN
    EXEC --> NAV
    EXEC --> VAL
    NAV --> AB
    AB --> ACTIONS
    ACTIONS --> DOM
    DOM --> BC
    BC --> PAGE
    PAGE --> PUP
    PUP --> CS
    CS --> INJECT
    
    PLAN --> LLM
    NAV --> LLM
    VAL --> LLM
    LLM --> PROMPT
    LLM --> PARSE
    
    EXEC --> MSG
    EXEC --> HIST
    EXEC --> EVENT
    
    style EXEC fill:#e1f5fe
    style LLM fill:#fff3e0
    style ACTIONS fill:#f3e5f5
    style BC fill:#ffebee
```

### 2.2 技术栈组成

#### 2.2.1 前端技术栈
```typescript
// 核心框架
- React 18.x              // UI框架
- TypeScript 5.x          // 类型安全
- Tailwind CSS           // 样式框架
- Vite                   // 构建工具

// Chrome Extension APIs
- chrome.tabs            // 标签页管理
- chrome.scripting       // 脚本注入
- chrome.storage         // 数据存储
- chrome.debugger        // 调试器API
```

#### 2.2.2 后端技术栈
```typescript
// AI/LLM集成
- LangChain              // LLM框架
- OpenAI API             // GPT模型
- Anthropic API          // Claude模型
- Zod                    // Schema验证

// 浏览器控制
- Puppeteer              // 浏览器自动化
- Chrome DevTools Protocol // 底层协议
- DOM API                // DOM操作

// 工具库
- jsonrepair             // JSON修复
- uuid                   // 唯一标识
- lodash                 // 工具函数
```

### 2.3 数据流架构

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as SidePanel
    participant BG as Background
    participant Executor as Executor
    participant Agent as Agent
    participant LLM as LLM服务
    participant Browser as 浏览器
    
    User->>UI: 输入任务
    UI->>BG: 发送new_task消息
    BG->>Executor: 创建执行器实例
    Executor->>Agent: 初始化Agent
    
    loop 执行循环
        Agent->>Browser: 获取页面状态
        Browser-->>Agent: 返回DOM状态
        Agent->>LLM: 发送状态+任务
        LLM-->>Agent: 返回Action序列
        Agent->>Browser: 执行Actions
        Browser-->>Agent: 返回执行结果
        Agent->>Executor: 报告进度
        Executor->>UI: 更新状态
    end
    
    Executor->>UI: 任务完成
    UI->>User: 显示结果
```

---

## 3. Agent模块详解

### 3.1 三Agent架构设计

nanobrowser采用了创新的三Agent协作架构，每个Agent承担不同的职责：

```mermaid
graph TD
    subgraph "PlannerAgent - 战略规划"
        P1[任务分析]
        P2[策略制定]
        P3[风险评估]
        P4[进度规划]
    end
    
    subgraph "NavigatorAgent - 战术执行"
        N1[状态理解]
        N2[Action生成]
        N3[DOM操作]
        N4[结果收集]
    end
    
    subgraph "ValidatorAgent - 质量保证"
        V1[结果验证]
        V2[任务完成度检查]
        V3[错误检测]
        V4[重试决策]
    end
    
    P1 --> P2 --> P3 --> P4
    P4 -.规划输出.-> N1
    N1 --> N2 --> N3 --> N4
    N4 -.执行结果.-> V1
    V1 --> V2 --> V3 --> V4
    V4 -.验证失败.-> N1
    
    style P1 fill:#e1f5fe
    style N1 fill:#fff3e0
    style V1 fill:#f3e5f5
```

### 3.2 BaseAgent抽象基类

```typescript
export abstract class BaseAgent<T extends z.ZodType, M = unknown> {
  // 核心属性
  protected id: string;                        // Agent唯一标识
  protected chatLLM: BaseChatModel;            // LLM实例
  protected prompt: BasePrompt;                // Prompt管理器
  protected context: AgentContext;             // 共享上下文
  protected actions: Record<string, Action>;   // Action注册表
  protected modelOutputSchema: T;              // 输出Schema验证
  protected withStructuredOutput: boolean;     // 结构化输出标志

  // 抽象方法 - 子类必须实现
  abstract execute(): Promise<AgentOutput<M>>;

  // 核心方法 - 统一的LLM调用逻辑
  async invoke(messages: BaseMessage[]): Promise<ModelOutput> {
    // 1. 优先使用结构化输出
    if (this.withStructuredOutput) {
      try {
        return await this.invokeWithJsonParser(messages);
      } catch (error) {
        logger.error('JsonOutputParser failed, falling back to manual extraction');
      }
    }
    
    // 2. 回退到手动JSON提取
    const response = await this.chatLLM.invoke(messages);
    const extractedJson = extractJsonFromModelOutput(response.content);
    return this.validateModelOutput(extractedJson);
  }

  // 结构化输出处理
  private async invokeWithJsonParser(messages: BaseMessage[]): Promise<ModelOutput> {
    const parser = new JsonOutputParser();
    const schemaDescription = this.getJsonSchemaDescription();
    
    const systemMessage = new SystemMessage(`You must respond with valid JSON that matches the expected schema.

Expected JSON schema:
${schemaDescription}

Important rules:
- Respond with valid JSON only
- Do not include any markdown formatting or code blocks
- Ensure all required fields are present
- Follow the exact field names and types specified`);
    
    const chain = this.chatLLM.pipe(parser);
    const result = await chain.invoke([systemMessage, ...messages]);
    return this.validateModelOutput(result);
  }
}
```

### 3.3 PlannerAgent - 任务规划智能体

#### 3.3.1 核心职责
- **任务分析**: 理解用户意图和任务复杂度
- **策略制定**: 制定多步骤执行计划
- **风险评估**: 识别潜在挑战和障碍
- **进度跟踪**: 监控任务执行进度

#### 3.3.2 输出Schema
```typescript
export const plannerOutputSchema = z.object({
  observation: z.string().describe("当前状态的详细观察"),
  challenges: z.string().describe("识别的潜在挑战和困难"),
  done: z.union([
    z.boolean(),
    z.string().transform(str => str.toLowerCase() === 'true')
  ]).describe("任务是否已完成"),
  next_steps: z.string().describe("下一步的具体行动计划"),
  reasoning: z.string().describe("决策的推理过程"),
  web_task: z.union([
    z.boolean(),
    z.string().transform(str => str.toLowerCase() === 'true')
  ]).describe("是否为Web相关任务"),
});
```

#### 3.3.3 执行流程
```mermaid
flowchart TD
    A[接收任务] --> B[分析当前状态]
    B --> C[识别任务类型]
    C --> D{是否为Web任务?}
    D -->|是| E[制定Web操作策略]
    D -->|否| F[制定通用策略]
    E --> G[评估执行难度]
    F --> G
    G --> H[生成执行计划]
    H --> I[输出规划结果]
    
    style A fill:#e1f5fe
    style D fill:#fff3e0
    style I fill:#e8f5e8
```

### 3.4 NavigatorAgent - 执行智能体

#### 3.4.1 核心职责
- **状态理解**: 解析当前浏览器状态
- **Action生成**: 根据任务生成具体操作序列
- **DOM操作**: 执行浏览器交互操作
- **结果收集**: 收集执行结果和反馈

#### 3.4.2 输出Schema
```typescript
export const navigatorOutputSchema = z.object({
  current_state: z.object({
    evaluation_previous_goal: z.string().describe("对前一个目标执行结果的评估"),
    memory: z.string().describe("需要记住的重要信息和已完成的工作"),
    next_goal: z.string().describe("下一个即时行动目标"),
  }),
  action: z.array(actionSchema).describe("要执行的动作序列"),
});
```

#### 3.4.3 Action执行机制
```typescript
async doMultiAction(actions: any[]): Promise<ActionResult[]> {
  const results: ActionResult[] = [];
  let cachedPathHashes: Set<string>;
  
  for (const [i, action] of actions.entries()) {
    // 1. DOM变化检测
    if (i > 0 && indexArg !== null) {
      const newState = await browserContext.getState();
      const newPathHashes = await calcBranchPathHashSet(newState);
      
      // 如果DOM结构发生变化，停止执行
      if (!newPathHashes.isSubsetOf(cachedPathHashes)) {
        const msg = `Something new appeared after action ${i} / ${actions.length}`;
        results.push(new ActionResult({
          extractedContent: msg,
          includeInMemory: true,
        }));
        break; // 让AI重新分析新状态
      }
    }
    
    // 2. 执行单个Action
    const actionInstance = this.actionRegistry.getAction(actionName);
    const result = await actionInstance.call(actionArgs);
    results.push(result);
    
    // 3. 检查是否完成任务
    if (result.isDone) {
      break;
    }
  }
  
  return results;
}
```

### 3.5 ValidatorAgent - 验证智能体

#### 3.5.1 核心职责
- **结果验证**: 检查任务执行结果的正确性
- **完成度评估**: 评估任务完成程度
- **错误检测**: 识别执行过程中的错误
- **重试决策**: 决定是否需要重新执行

#### 3.5.2 输出Schema
```typescript
export const validatorOutputSchema = z.object({
  is_valid: z.boolean().describe("验证结果是否有效"),
  reason: z.string().describe("验证结果的详细原因"),
  answer: z.string().describe("任务的最终答案或结果"),
});
```

#### 3.5.3 验证流程
```mermaid
flowchart TD
    A[接收执行结果] --> B[分析任务目标]
    B --> C[检查结果完整性]
    C --> D[验证结果准确性]
    D --> E{验证通过?}
    E -->|是| F[标记任务完成]
    E -->|否| G[分析失败原因]
    G --> H[生成重试建议]
    H --> I[返回验证结果]
    F --> I
    
    style A fill:#e1f5fe
    style E fill:#fff3e0
    style F fill:#e8f5e8
    style G fill:#ffebee
```

---

## 4. 技术实现机制

### 4.1 Chrome Extension架构

#### 4.1.1 Extension组件结构
```
nanobrowser-extension/
├── manifest.json           # 扩展配置文件
├── background/             # 后台脚本
│   ├── index.ts           # 主入口
│   ├── agent/             # Agent系统
│   └── browser/           # 浏览器控制
├── content/               # 内容脚本
│   ├── index.ts           # DOM操作
│   └── injected.ts        # 页面注入脚本
├── sidepanel/             # 侧边栏UI
│   ├── index.tsx          # React应用
│   └── components/        # UI组件
└── popup/                 # 弹窗UI
    └── index.tsx          # 简单界面
```

#### 4.1.2 权限配置
```json
{
  "manifest_version": 3,
  "permissions": [
    "storage",              // 本地存储
    "scripting",            // 脚本注入
    "tabs",                 // 标签页管理
    "activeTab",            // 活动标签页访问
    "debugger",             // 调试器API (Puppeteer需要)
    "unlimitedStorage",     // 无限存储
    "sidePanel"             // 侧边栏API
  ],
  "host_permissions": ["<all_urls>"], // 所有网站访问权限
  "content_security_policy": {
    "extension_pages": "script-src 'self'; object-src 'self';"
  }
}
```

### 4.2 Puppeteer集成机制

#### 4.2.1 连接建立
```typescript
// Page.attachPuppeteer() - 核心连接机制
async attachPuppeteer(): Promise<boolean> {
  if (!this._validWebPage) {
    return false;
  }

  logger.info('attaching puppeteer', this._tabId);
  
  try {
    // 1. 通过ExtensionTransport连接到Chrome标签页
    const browser = await connect({
      transport: await ExtensionTransport.connectTab(this._tabId),
      defaultViewport: null,
      protocol: 'cdp' as ProtocolType, // Chrome DevTools Protocol
    });
    this._browser = browser;

    // 2. 获取页面实例
    const [page] = await browser.pages();
    this._puppeteerPage = page;

    // 3. 添加反检测脚本
    await this._addAntiDetectionScripts();

    // 4. 设置事件监听
    await this._setupEventListeners();

    return true;
  } catch (error) {
    logger.error('Failed to attach puppeteer:', error);
    return false;
  }
}
```

#### 4.2.2 反检测机制
```typescript
private async _addAntiDetectionScripts(): Promise<void> {
  await this._puppeteerPage.evaluateOnNewDocument(`
    // 1. 隐藏webdriver属性
    Object.defineProperty(navigator, 'webdriver', {
      get: () => undefined
    });

    // 2. 模拟Chrome运行时
    window.chrome = window.chrome || { runtime: {} };

    // 3. 权限查询拦截
    const originalQuery = window.navigator.permissions.query;
    window.navigator.permissions.query = (parameters) => (
      parameters.name === 'notifications' ?
        Promise.resolve({ state: Notification.permission }) :
        originalQuery(parameters)
    );

    // 4. Shadow DOM开放模式
    const originalAttachShadow = Element.prototype.attachShadow;
    Element.prototype.attachShadow = function attachShadow(options) {
      return originalAttachShadow.call(this, { ...options, mode: "open" });
    };

    // 5. 插件检测绕过
    Object.defineProperty(navigator, 'plugins', {
      get: () => [1, 2, 3, 4, 5]
    });

    // 6. 语言设置
    Object.defineProperty(navigator, 'languages', {
      get: () => ['en-US', 'en']
    });
  `);
}
```

### 4.3 DOM操作引擎

#### 4.3.1 DOM树构建算法
```javascript
function buildDomTree(node, parentIframe = null, isParentHighlighted = false) {
  // 1. 快速过滤检查
  if (!node || node.id === HIGHLIGHT_CONTAINER_ID || 
      (node.nodeType !== Node.ELEMENT_NODE && node.nodeType !== Node.TEXT_NODE)) {
    return null;
  }

  // 2. 视口裁剪优化
  if (node.nodeType === Node.ELEMENT_NODE) {
    const rect = getCachedBoundingClientRect(node);
    const isFixedOrSticky = isElementFixedOrSticky(node);
    const hasSize = rect && (rect.width > 0 || rect.height > 0);
    
    // 快速排除视口外元素
    if (!rect || (!isFixedOrSticky && !hasSize && 
        (rect.bottom < -viewportExpansion || rect.top > window.innerHeight + viewportExpansion))) {
      return null;
    }
  }

  // 3. 构建节点数据
  const nodeData = {
    tagName: node.tagName?.toLowerCase(),
    attributes: extractAttributes(node),
    xpath: getXPathTree(node, true),
    children: [],
    isVisible: false,
    isTopElement: false,
    isInteractive: false,
  };

  // 4. 可见性和交互性检测
  if (node.nodeType === Node.ELEMENT_NODE) {
    nodeData.isVisible = isElementVisible(node);
    if (nodeData.isVisible) {
      nodeData.isTopElement = isTopElement(node);
      if (nodeData.isTopElement) {
        nodeData.isInteractive = isInteractiveElement(node);
        // 高亮处理
        nodeWasHighlighted = handleHighlighting(nodeData, node, parentIframe, isParentHighlighted);
      }
    }
  }

  // 5. 递归处理子元素
  for (const child of node.childNodes) {
    const passHighlightStatusToChild = nodeWasHighlighted || isParentHighlighted;
    const domElement = buildDomTree(child, parentIframe, passHighlightStatusToChild);
    if (domElement) nodeData.children.push(domElement);
  }

  return nodeData;
}
```

#### 4.3.2 智能元素定位
```typescript
async locateElement(element: DOMElementNode): Promise<ElementHandle | null> {
  if (!this._puppeteerPage) {
    return null;
  }
  
  let currentFrame: PuppeteerPage | Frame = this._puppeteerPage;

  // 1. 处理iframe嵌套
  const parents: DOMElementNode[] = [];
  let current = element;
  while (current.parent) {
    parents.push(current.parent);
    current = current.parent;
  }

  // 2. 从根节点向下定位
  parents.reverse();
  for (const parent of parents) {
    if (parent.tagName === 'iframe') {
      const iframeHandle = await currentFrame.$(parent.getCssSelector());
      if (iframeHandle) {
        const frame = await iframeHandle.contentFrame();
        if (frame) currentFrame = frame;
      }
    }
  }

  // 3. 多重定位策略
  try {
    // 策略1: CSS选择器
    const cssSelector = element.getCssSelector();
    let elementHandle = await currentFrame.$(cssSelector);
    
    // 策略2: XPath选择器（回退）
    if (!elementHandle && element.xpath) {
      const xpathSelector = `::-p-xpath(${element.xpath})`;
      elementHandle = await currentFrame.$(xpathSelector);
    }
    
    // 策略3: 属性匹配（最后回退）
    if (!elementHandle) {
      elementHandle = await this._locateByAttributes(currentFrame, element);
    }
    
    // 4. 检查可见性并滚动到视图
    if (elementHandle) {
      const isHidden = await elementHandle.isHidden();
      if (!isHidden) {
        await this._scrollIntoViewIfNeeded(elementHandle);
      }
    }
    
    return elementHandle;
  } catch (error) {
    logger.error('Failed to locate element:', error);
    return null;
  }
}
```

---

## 5. 关键流程分析

### 5.1 任务执行完整流程

#### 5.1.1 任务生命周期
```mermaid
stateDiagram-v2
    [*] --> TaskReceived: 用户输入任务
    TaskReceived --> Initializing: 创建Executor
    Initializing --> Planning: 初始化完成
    Planning --> Executing: 规划完成
    Executing --> Validating: 执行完成
    Validating --> Completed: 验证通过
    Validating --> Executing: 验证失败
    Executing --> Failed: 连续失败
    Completed --> [*]
    Failed --> [*]

    Planning --> Failed: 规划失败
    Executing --> Planning: 需要重新规划
```

#### 5.1.2 详细执行流程
```typescript
// Executor.execute() - 主执行循环
async execute(): Promise<void> {
  const context = this.context;
  context.nSteps = 0;
  let done = false;

  try {
    // 发射任务开始事件
    context.emitEvent(Actors.SYSTEM, ExecutionState.TASK_START, 'Task execution started');

    while (context.nSteps < context.options.maxSteps && !done && !context.stopped) {
      // 检查暂停状态
      if (context.paused) {
        await this.waitForResume();
        continue;
      }

      // 1. 规划阶段 (每3步执行一次)
      if (context.nSteps % context.options.planningInterval === 0) {
        try {
          const planOutput = await this.planner.execute();
          if (planOutput.result) {
            const plan = { ...planOutput.result };
            context.messageManager.addPlan(JSON.stringify(plan));

            // 检查是否已完成
            if (planOutput.result.done) {
              done = true;
              continue;
            }
          }
        } catch (error) {
          logger.error('Planning failed:', error);
          // 规划失败不中断执行，继续导航
        }
      }

      // 2. 导航执行阶段
      done = await this.navigate();

      // 3. 验证阶段 (任务完成时)
      if (done && context.options.validateOutput) {
        try {
          const validationOutput = await this.validator.execute();
          if (validationOutput.result && !validationOutput.result.is_valid) {
            done = false; // 验证失败，继续执行
            context.consecutiveValidatorFailures++;

            if (context.consecutiveValidatorFailures >= context.options.maxValidatorFailures) {
              throw new Error('Too many consecutive validator failures');
            }
          } else {
            // 验证通过，重置失败计数
            context.consecutiveValidatorFailures = 0;
          }
        } catch (error) {
          logger.error('Validation failed:', error);
          // 验证失败，继续执行
          done = false;
        }
      }

      // 4. 检查取消信号
      if (context.controller.signal.aborted) {
        throw new RequestCancelledError('Task was cancelled');
      }
    }

    // 任务完成
    if (done) {
      context.emitEvent(Actors.SYSTEM, ExecutionState.TASK_OK, 'Task completed successfully');
    } else if (context.nSteps >= context.options.maxSteps) {
      context.emitEvent(Actors.SYSTEM, ExecutionState.TASK_FAIL, 'Task failed: Maximum steps reached');
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    context.emitEvent(Actors.SYSTEM, ExecutionState.TASK_FAIL, errorMessage);
    throw error;
  }
}
```

### 5.2 AI决策流程详解

#### 5.2.1 状态理解机制
```mermaid
flowchart TD
    A[获取浏览器状态] --> B[构建DOM树]
    B --> C[识别可交互元素]
    C --> D[生成元素索引]
    D --> E[格式化状态描述]
    E --> F[添加滚动信息]
    F --> G[包装安全标签]
    G --> H[构建完整Prompt]
    H --> I{支持视觉?}
    I -->|是| J[添加截图]
    I -->|否| K[纯文本输入]
    J --> L[发送给LLM]
    K --> L

    style A fill:#e1f5fe
    style H fill:#fff3e0
    style L fill:#e8f5e8
```

#### 5.2.2 AI看到的状态格式
```
[Task history memory ends]
[Current state starts here]
Current tab: {id: 123, url: "https://amazon.com", title: "Amazon.com - Online Shopping"}
Other available tabs:
  - {id: 124, url: "https://google.com", title: "Google"}
  - {id: 125, url: "https://github.com", title: "GitHub"}

Interactive elements from top layer of the current page inside the viewport:
[Scroll info] window.scrollY: 0, document.body.scrollHeight: 2400, visual viewport height: 800
[Start of page]
<nano_untrusted_content>
<click>0 "Amazon" <select>1 "All Departments" <input>2 "Search Amazon" <click>3 "Search"
<click>4 "Account & Lists" <click>5 "Returns & Orders" <click>6 "Cart (0)"
<click>7 "Today's Deals" <click>8 "Customer Service" <click>9 "Registry"
<click>10 "Gift Cards" <click>11 "Sell"
</nano_untrusted_content>
[End of page]

Step 1 of 100
Previous action results:
- Navigated to Amazon homepage successfully
```

#### 5.2.3 LLM输出解析机制
```typescript
// 双重解析策略
async invoke(inputMessages: BaseMessage[]): Promise<NavigatorResult> {
  // 1. 优先使用结构化输出
  if (this.withStructuredOutput) {
    try {
      return await this.invokeWithJsonParser(inputMessages);
    } catch (error) {
      logger.error('JsonOutputParser failed, falling back to manual extraction');
      // 回退到手动解析
    }
  }

  // 2. 手动JSON提取
  const response = await this.chatLLM.invoke(inputMessages);
  const extractedJson = extractJsonFromModelOutput(response.content);
  return this.validateModelOutput(extractedJson);
}

// JSON提取函数
function extractJsonFromModelOutput(content: string): unknown {
  // 1. 尝试直接解析
  try {
    return JSON.parse(content);
  } catch {
    // 继续其他方法
  }

  // 2. 提取JSON代码块
  const jsonBlockMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/i);
  if (jsonBlockMatch) {
    try {
      return JSON.parse(jsonBlockMatch[1]);
    } catch {
      // 继续其他方法
    }
  }

  // 3. 查找JSON对象
  const jsonMatch = content.match(/\{[\s\S]*\}/);
  if (jsonMatch) {
    try {
      return JSON.parse(jsonMatch[0]);
    } catch {
      // 使用JSON修复
      const repairedJson = repairJsonString(jsonMatch[0]);
      return JSON.parse(repairedJson);
    }
  }

  throw new Error('No valid JSON found in model output');
}
```

### 5.3 Action执行机制

#### 5.3.1 26个内置Action详解

| 分类 | Action名称 | 功能描述 | 参数 |
|------|------------|----------|------|
| **任务控制** | done | 完成任务 | text, success |
| **导航操作** | search_google | Google搜索 | query |
| | go_to_url | 导航到URL | url |
| | go_back | 返回上一页 | - |
| **元素交互** | click_element | 点击元素 | index, intent, xpath |
| | input_text | 输入文本 | index, text, intent, xpath |
| | select_dropdown_option | 选择下拉选项 | index, text, intent, xpath |
| | get_dropdown_options | 获取下拉选项 | index, intent, xpath |
| **页面操作** | scroll_to_text | 滚动到文本 | text |
| | scroll_to_percent | 滚动到百分比 | percent |
| | scroll_to_top | 滚动到顶部 | - |
| | scroll_to_bottom | 滚动到底部 | - |
| | next_page | 下一页 | - |
| | previous_page | 上一页 | - |
| **标签管理** | switch_tab | 切换标签页 | tab_id |
| | open_tab | 打开新标签页 | url |
| | close_tab | 关闭标签页 | tab_id |
| **键盘操作** | send_keys | 发送按键 | keys |
| **内容操作** | cache_content | 缓存内容 | - |
| **等待操作** | wait | 等待 | seconds |

#### 5.3.2 Action执行示例 - input_text
```typescript
const inputText = new Action(
  async (input: z.infer<typeof inputTextActionSchema.schema>) => {
    // 1. 事件发射
    const intent = input.intent || `Input text into index ${input.index}`;
    this.context.emitEvent(Actors.NAVIGATOR, ExecutionState.ACT_START, intent);

    // 2. 获取页面状态
    const page = await this.context.browserContext.getCurrentPage();
    const state = await page.getState();

    // 3. 定位元素
    const elementNode = state?.selectorMap.get(input.index);
    if (!elementNode) {
      throw new Error(`Element with index ${input.index} does not exist`);
    }

    // 4. 执行输入操作
    await page.inputTextElementNode(this.context.options.useVision, elementNode, input.text);

    // 5. 返回结果
    const msg = `Input "${input.text}" into index ${input.index}`;
    this.context.emitEvent(Actors.NAVIGATOR, ExecutionState.ACT_OK, msg);
    return new ActionResult({
      extractedContent: msg,
      includeInMemory: true,
      interactedElement: elementNode
    });
  },
  inputTextActionSchema,
  true, // hasIndex = true
);
```

### 5.4 错误处理与恢复机制

#### 5.4.1 错误分类体系
```mermaid
graph TD
    A[错误类型] --> B[认证错误]
    A --> C[权限错误]
    A --> D[网络错误]
    A --> E[DOM错误]
    A --> F[扩展冲突错误]
    A --> G[任务取消错误]

    B --> B1[ChatModelAuthError]
    C --> C1[ChatModelForbiddenError]
    D --> D1[NetworkError]
    E --> E1[ElementNotFoundError]
    E --> E2[InvalidInputError]
    F --> F1[ExtensionConflictError]
    G --> G1[RequestCancelledError]

    style A fill:#e1f5fe
    style B1 fill:#ffebee
    style C1 fill:#ffebee
    style E1 fill:#fff3e0
    style F1 fill:#f3e5f5
```

#### 5.4.2 智能重试机制
```typescript
// 连续失败处理
private async navigate(): Promise<boolean> {
  try {
    const navOutput = await this.navigator.execute();
    this.context.consecutiveFailures = 0; // 重置失败计数
    this.context.nSteps++;

    if (navOutput.result?.done) {
      return true; // 任务完成
    }
  } catch (error) {
    this.context.consecutiveFailures++;

    // 错误分类处理
    if (isAuthenticationError(error)) {
      throw new ChatModelAuthError('API Authentication failed', error);
    }
    if (isForbiddenError(error)) {
      throw new ChatModelForbiddenError('API access forbidden', error);
    }
    if (isAbortedError(error)) {
      throw new RequestCancelledError('Request was cancelled');
    }
    if (isExtensionConflictError(error)) {
      throw new ExtensionConflictError('Extension conflict detected');
    }

    // 连续失败检查
    if (this.context.consecutiveFailures >= this.context.options.maxFailures) {
      throw new Error(`Too many consecutive failures: ${this.context.consecutiveFailures}`);
    }

    // 记录错误但继续尝试
    logger.error(`Navigation failed (attempt ${this.context.consecutiveFailures}):`, error);

    // 添加延迟重试
    await new Promise(resolve => setTimeout(resolve, this.context.options.retryDelay));
  }

  return false;
}
```

### 5.5 消息管理与上下文维护

#### 5.5.1 消息生命周期
```mermaid
sequenceDiagram
    participant Task as 任务输入
    participant MM as MessageManager
    participant Hist as MessageHistory
    participant LLM as LLM服务
    participant Token as Token管理

    Task->>MM: 初始化任务消息
    MM->>Hist: 添加系统消息
    MM->>Hist: 添加任务描述
    MM->>Hist: 添加历史标记

    loop 执行循环
        MM->>Hist: 添加状态消息
        MM->>Token: 检查Token限制
        Token->>Hist: 裁剪历史消息
        Hist->>LLM: 发送消息历史
        LLM-->>MM: 返回AI输出
        MM->>Hist: 添加AI输出
        MM->>Hist: 添加执行结果
    end

    MM->>Hist: 添加完成标记
```

#### 5.5.2 智能Token管理
```typescript
// Token估算和裁剪
private trimMessagesToFitTokenLimit(): void {
  while (this.history.totalTokens > this.settings.maxInputTokens && this.history.messages.length > 1) {
    // 保护初始化消息，从最旧的非初始化消息开始删除
    const firstNonInitIndex = this.history.messages.findIndex(m => m.metadata.type !== 'init');
    if (firstNonInitIndex !== -1) {
      const removedMessage = this.history.messages[firstNonInitIndex];
      this.history.removeMessage(firstNonInitIndex);
      logger.debug(`Removed message to fit token limit. Tokens: ${removedMessage.metadata.tokens}`);
    } else {
      break; // 只剩初始化消息，停止删除
    }
  }
}

// Token估算
private estimateTokens(message: BaseMessage): number {
  let tokens = 0;

  if (typeof message.content === 'string') {
    // 文本内容：字符数 / 每Token字符数
    tokens += Math.ceil(message.content.length / this.settings.estimatedCharactersPerToken);
  } else if (Array.isArray(message.content)) {
    // 多模态内容
    for (const item of message.content) {
      if (item.type === 'text') {
        tokens += Math.ceil(item.text.length / this.settings.estimatedCharactersPerToken);
      } else if (item.type === 'image_url') {
        tokens += this.settings.imageTokens; // 图片固定Token数
      }
    }
  }

  return tokens;
}
```

---

## 6. 部署与配置

### 6.1 开发环境搭建

#### 6.1.1 环境要求
```bash
# 系统要求
- Node.js >= 18.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0
- Chrome/Chromium >= 120.0.0
- TypeScript >= 5.0.0

# 开发工具
- VS Code (推荐)
- Chrome DevTools
- React DevTools
```

#### 6.1.2 项目初始化
```bash
# 1. 克隆项目
git clone https://github.com/your-org/nanobrowser.git
cd nanobrowser

# 2. 安装依赖
npm install
# 或
yarn install

# 3. 环境配置
cp .env.example .env
# 编辑 .env 文件，配置API密钥

# 4. 构建项目
npm run build
# 或
yarn build

# 5. 开发模式
npm run dev
# 或
yarn dev
```

#### 6.1.3 Chrome Extension安装
```bash
# 1. 打开Chrome扩展管理页面
chrome://extensions/

# 2. 启用开发者模式
点击右上角的"开发者模式"开关

# 3. 加载扩展
点击"加载已解压的扩展程序"
选择项目的 dist/ 目录

# 4. 验证安装
在Chrome工具栏中应该看到nanobrowser图标
```

### 6.2 配置管理

#### 6.2.1 环境变量配置
```typescript
// .env 文件配置
VITE_OPENAI_API_KEY=sk-...                    // OpenAI API密钥
VITE_ANTHROPIC_API_KEY=sk-ant-...             // Anthropic API密钥
VITE_DEFAULT_LLM_PROVIDER=openai              // 默认LLM提供商
VITE_DEFAULT_MODEL=gpt-4                      // 默认模型
VITE_MAX_STEPS=100                            // 最大执行步数
VITE_MAX_ACTIONS_PER_STEP=10                  // 每步最大Action数
VITE_USE_VISION=false                         // 是否启用视觉
VITE_VALIDATE_OUTPUT=false                    // 是否验证输出
VITE_DEBUG_MODE=false                         // 调试模式
VITE_LOG_LEVEL=info                           // 日志级别
```

#### 6.2.2 Agent配置选项
```typescript
export interface AgentOptions {
  maxSteps: number;                // 最大执行步数 (默认: 100)
  maxActionsPerStep: number;       // 每步最大Action数 (默认: 10)
  maxFailures: number;             // 最大失败次数 (默认: 5)
  maxValidatorFailures: number;    // 最大验证失败次数 (默认: 3)
  retryDelay: number;              // 重试延迟毫秒 (默认: 1000)
  maxInputTokens: number;          // 最大输入Token数 (默认: 128000)
  maxErrorLength: number;          // 最大错误长度 (默认: 400)
  useVision: boolean;              // 是否使用视觉 (默认: false)
  useVisionForPlanner: boolean;    // 规划器是否使用视觉 (默认: false)
  validateOutput: boolean;         // 是否验证输出 (默认: false)
  includeAttributes: string[];     // 包含的属性 (默认: [])
  planningInterval: number;        // 规划间隔 (默认: 3)
}
```

### 6.3 生产部署

#### 6.3.1 构建优化
```bash
# 生产构建
npm run build:prod

# 构建分析
npm run analyze

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 测试
npm run test
```

#### 6.3.2 Chrome Web Store发布
```bash
# 1. 准备发布包
npm run build:prod
npm run package

# 2. 创建发布包
zip -r nanobrowser-extension.zip dist/

# 3. 上传到Chrome Web Store
# 访问 https://chrome.google.com/webstore/devconsole/
# 上传 nanobrowser-extension.zip
# 填写应用信息和描述
# 提交审核
```

---

## 7. 性能与监控

### 7.1 性能指标

#### 7.1.1 关键性能指标(KPI)
```typescript
interface PerformanceMetrics {
  // 执行性能
  taskCompletionRate: number;      // 任务完成率 (%)
  averageExecutionTime: number;    // 平均执行时间 (秒)
  averageStepsPerTask: number;     // 平均步数
  actionSuccessRate: number;       // Action成功率 (%)

  // 资源使用
  memoryUsage: number;             // 内存使用 (MB)
  cpuUsage: number;                // CPU使用率 (%)
  tokenConsumption: number;        // Token消耗量
  apiCallCount: number;            // API调用次数

  // 错误统计
  errorRate: number;               // 错误率 (%)
  retryRate: number;               // 重试率 (%)
  timeoutRate: number;             // 超时率 (%)

  // 用户体验
  responseTime: number;            // 响应时间 (毫秒)
  userSatisfactionScore: number;   // 用户满意度评分
}
```

#### 7.1.2 性能监控实现
```typescript
class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private startTime: number;

  startTask(taskId: string): void {
    this.startTime = Date.now();
    this.metrics = this.initializeMetrics();

    // 记录任务开始
    logger.info(`Task ${taskId} started`, {
      timestamp: this.startTime,
      memoryUsage: this.getMemoryUsage(),
    });
  }

  recordAction(actionName: string, success: boolean, duration: number): void {
    this.metrics.actionSuccessRate = this.updateSuccessRate(success);

    // 记录Action执行
    logger.debug(`Action ${actionName} ${success ? 'succeeded' : 'failed'}`, {
      duration,
      memoryUsage: this.getMemoryUsage(),
    });
  }

  endTask(taskId: string, success: boolean): PerformanceMetrics {
    const endTime = Date.now();
    const executionTime = endTime - this.startTime;

    this.metrics.taskCompletionRate = success ? 100 : 0;
    this.metrics.averageExecutionTime = executionTime / 1000;

    // 记录任务完成
    logger.info(`Task ${taskId} ${success ? 'completed' : 'failed'}`, {
      executionTime,
      finalMetrics: this.metrics,
    });

    return this.metrics;
  }

  private getMemoryUsage(): number {
    if (performance.memory) {
      return performance.memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  }
}
```

### 7.2 日志系统

#### 7.2.1 日志级别定义
```typescript
enum LogLevel {
  ERROR = 0,    // 错误信息
  WARN = 1,     // 警告信息
  INFO = 2,     // 一般信息
  DEBUG = 3,    // 调试信息
  TRACE = 4,    // 跟踪信息
}

interface LogEntry {
  timestamp: number;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  error?: Error;
  taskId?: string;
  stepId?: string;
  actionId?: string;
}
```

#### 7.2.2 结构化日志实现
```typescript
class Logger {
  private logLevel: LogLevel;
  private logBuffer: LogEntry[] = [];

  constructor(level: LogLevel = LogLevel.INFO) {
    this.logLevel = level;
  }

  error(message: string, context?: Record<string, any>, error?: Error): void {
    this.log(LogLevel.ERROR, message, context, error);
  }

  warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context);
  }

  info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context);
  }

  debug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  private log(level: LogLevel, message: string, context?: Record<string, any>, error?: Error): void {
    if (level <= this.logLevel) {
      const entry: LogEntry = {
        timestamp: Date.now(),
        level,
        message,
        context,
        error,
      };

      this.logBuffer.push(entry);
      this.outputLog(entry);

      // 保持日志缓冲区大小
      if (this.logBuffer.length > 1000) {
        this.logBuffer.shift();
      }
    }
  }

  private outputLog(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString();
    const levelName = LogLevel[entry.level];
    const contextStr = entry.context ? JSON.stringify(entry.context) : '';

    const logMessage = `[${timestamp}] ${levelName}: ${entry.message} ${contextStr}`;

    switch (entry.level) {
      case LogLevel.ERROR:
        console.error(logMessage, entry.error);
        break;
      case LogLevel.WARN:
        console.warn(logMessage);
        break;
      case LogLevel.INFO:
        console.info(logMessage);
        break;
      case LogLevel.DEBUG:
      case LogLevel.TRACE:
        console.debug(logMessage);
        break;
    }
  }
}
```

### 7.3 错误追踪

#### 7.3.1 错误分类统计
```typescript
interface ErrorStatistics {
  totalErrors: number;
  errorsByType: Record<string, number>;
  errorsByAgent: Record<string, number>;
  errorsByAction: Record<string, number>;
  criticalErrors: number;
  recoveredErrors: number;
}

class ErrorTracker {
  private statistics: ErrorStatistics;

  constructor() {
    this.statistics = this.initializeStatistics();
  }

  recordError(error: Error, context: {
    agent?: string;
    action?: string;
    taskId?: string;
    stepId?: string;
  }): void {
    this.statistics.totalErrors++;

    // 按类型统计
    const errorType = error.constructor.name;
    this.statistics.errorsByType[errorType] = (this.statistics.errorsByType[errorType] || 0) + 1;

    // 按Agent统计
    if (context.agent) {
      this.statistics.errorsByAgent[context.agent] = (this.statistics.errorsByAgent[context.agent] || 0) + 1;
    }

    // 按Action统计
    if (context.action) {
      this.statistics.errorsByAction[context.action] = (this.statistics.errorsByAction[context.action] || 0) + 1;
    }

    // 判断是否为关键错误
    if (this.isCriticalError(error)) {
      this.statistics.criticalErrors++;
    }

    // 记录详细错误信息
    logger.error('Error recorded', {
      errorType,
      message: error.message,
      stack: error.stack,
      context,
      statistics: this.statistics,
    });
  }

  recordRecovery(errorType: string): void {
    this.statistics.recoveredErrors++;
    logger.info('Error recovery recorded', {
      errorType,
      recoveryRate: this.statistics.recoveredErrors / this.statistics.totalErrors,
    });
  }

  private isCriticalError(error: Error): boolean {
    const criticalErrorTypes = [
      'ChatModelAuthError',
      'ExtensionConflictError',
      'URLNotAllowedError',
    ];
    return criticalErrorTypes.includes(error.constructor.name);
  }
}
```
