# nanobrowser Agent架构深度分析

## 🏗️ 整体架构概览

nanobrowser采用了**多Agent协作**的架构设计，通过三个专门的Agent协同工作来完成复杂的浏览器自动化任务：

```
┌─────────────────────────────────────────────────────────────┐
│                    AgentExecutor                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Planner   │  │  Navigator  │  │  Validator  │         │
│  │   Agent     │  │   Agent     │  │   Agent     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│         │                │                │                │
│         └────────────────┼────────────────┘                │
│                          │                                 │
│  ┌─────────────────────────────────────────────────────────┤
│  │              AgentContext                               │
│  │  • BrowserContext  • MessageManager  • EventManager    │
│  └─────────────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────────────┘
```

## 🧠 核心组件详解

### 1. BaseAgent - 基础抽象类

**设计模式**: 模板方法模式 + 策略模式

```typescript
export abstract class BaseAgent<T extends z.ZodType, M = unknown> {
  // 核心属性
  protected chatLLM: BaseChatModel;           // LLM实例
  protected prompt: BasePrompt;               // 提示词管理
  protected context: AgentContext;            // 共享上下文
  protected modelOutputSchema: T;             // 输出schema验证
  protected withStructuredOutput: boolean;    // 是否使用结构化输出
  
  // 核心方法
  abstract execute(): Promise<AgentOutput<M>>;  // 子类必须实现
  async invoke(messages): Promise<ModelOutput>; // LLM调用逻辑
  private invokeWithJsonParser(): Promise<ModelOutput>; // JSON解析方法
}
```

**关键特性**:
- ✅ **类型安全**: 使用Zod schema确保输出类型安全
- ✅ **结构化输出**: 支持JsonOutputParser和手动JSON提取
- ✅ **错误处理**: 完整的错误处理和重试机制
- ✅ **可扩展性**: 子类只需实现execute方法

### 2. PlannerAgent - 规划智能体

**职责**: 分析任务，制定执行计划，判断任务完成状态

```typescript
// 输出Schema
const plannerOutputSchema = z.object({
  observation: z.string(),    // 当前状态观察
  challenges: z.string(),     // 遇到的挑战
  done: z.boolean(),         // 是否完成
  next_steps: z.string(),    // 下一步计划
  reasoning: z.string(),     // 推理过程
  web_task: z.boolean(),     // 是否为网页任务
});
```

**工作流程**:
1. 接收任务描述和当前浏览器状态
2. 分析任务进度和遇到的问题
3. 制定下一步行动计划
4. 判断任务是否完成

### 3. NavigatorAgent - 导航执行智能体

**职责**: 执行具体的浏览器操作，是最复杂的Agent

```typescript
// 输出Schema (动态生成)
const navigatorSchema = z.object({
  current_state: agentBrainSchema,  // 当前状态评估
  action: z.array(actionSchema),    // 要执行的动作序列
});
```

**核心特性**:
- 🎯 **动作注册系统**: 通过NavigatorActionRegistry管理所有可用动作
- 🔄 **批量动作执行**: 支持一次执行多个动作序列
- 📝 **状态历史**: 维护浏览器状态变化历史
- 🔧 **动作修复**: 自动修复DOM变化导致的索引问题

### 4. ValidatorAgent - 验证智能体

**职责**: 验证任务执行结果是否正确

```typescript
const validatorOutputSchema = z.object({
  is_valid: z.boolean(),  // 是否有效
  reason: z.string(),     // 验证原因
  answer: z.string(),     // 最终答案
});
```

## 🎯 Action系统架构

### Action类设计

```typescript
export class Action {
  constructor(
    private readonly handler: (input: any) => Promise<ActionResult>,
    public readonly schema: ActionSchema,
    public readonly hasIndex: boolean = false,
  ) {}
  
  async call(input: any): Promise<ActionResult>
  getIndexArg(args: any): number | null
  setIndexArg(args: any, index: number): void
}
```

### 内置动作类型

| 类别 | 动作 | 描述 |
|------|------|------|
| **导航** | `go_to_url` | 导航到指定URL |
| | `go_back` | 返回上一页 |
| | `search_google` | Google搜索 |
| **元素交互** | `click_element` | 点击元素 |
| | `input_text` | 输入文本 |
| | `select_dropdown_option` | 选择下拉选项 |
| **页面操作** | `scroll_to_text` | 滚动到文本 |
| | `scroll_to_percent` | 滚动到百分比位置 |
| | `wait` | 等待指定时间 |
| **标签管理** | `switch_tab` | 切换标签页 |
| | `open_tab` | 打开新标签页 |
| | `close_tab` | 关闭标签页 |
| **任务控制** | `done` | 完成任务 |

### ActionBuilder工厂模式

```typescript
export class ActionBuilder {
  constructor(context: AgentContext, extractorLLM: BaseChatModel) {}
  
  buildDefaultActions(): Action[] {
    // 构建所有默认动作
    // 每个动作都有完整的错误处理和事件发射
  }
}
```

## 📝 Prompt系统架构

### BasePrompt抽象类

```typescript
abstract class BasePrompt {
  abstract getSystemMessage(): SystemMessage;
  abstract getUserMessage(context: AgentContext): Promise<HumanMessage>;
  
  // 构建包含浏览器状态的用户消息
  async buildBrowserStateUserMessage(context: AgentContext): Promise<HumanMessage>
}
```

### 提示词模板系统

- **NavigatorPrompt**: 包含动作执行指令和JSON格式要求
- **PlannerPrompt**: 包含任务规划和状态分析指令  
- **ValidatorPrompt**: 包含结果验证和答案提取指令
- **安全规则**: 通过`commonSecurityRules`防止提示词注入

## 🔄 执行流程

### 主要执行循环

```typescript
async execute(): Promise<void> {
  while (context.nSteps < allowedMaxSteps && !done) {
    // 1. 规划阶段 (每3步执行一次)
    if (context.nSteps % context.options.planningInterval === 0) {
      const planOutput = await this.planner.execute();
      // 处理规划结果
    }
    
    // 2. 导航执行阶段
    const navOutput = await this.navigator.execute();
    if (navOutput.result?.done) {
      done = true;
    }
    
    // 3. 验证阶段 (可选)
    if (context.options.validateOutput && done) {
      const validationOutput = await this.validator.execute();
      if (!validationOutput.result?.is_valid) {
        done = false; // 继续执行
      }
    }
    
    context.nSteps++;
  }
}
```

## 💾 消息管理系统

### MessageManager核心功能

```typescript
export class MessageManager {
  // 消息历史管理
  private history: MessageHistory;
  private settings: MessageManagerSettings;
  
  // 核心方法
  initTaskMessages(systemMessage, task, context?): void
  addModelOutput(modelOutput): void
  addPlan(plan, position): void
  addNewTask(task): void
  
  // Token管理
  private estimateTokens(message): number
  private trimMessagesToFitTokenLimit(): void
}
```

### 消息类型和标签系统

- **安全标签**: `<nano_user_request>` 和 `<nano_untrusted_content>`
- **消息类型**: SystemMessage, HumanMessage, AIMessage, ToolMessage
- **Token限制**: 自动裁剪消息以适应模型token限制

## 🎛️ 上下文管理

### AgentContext - 共享状态

```typescript
export class AgentContext {
  controller: AbortController;           // 取消控制
  browserContext: BrowserContext;       // 浏览器上下文
  messageManager: MessageManager;       // 消息管理
  eventManager: EventManager;           // 事件管理
  options: AgentOptions;                // 配置选项
  
  // 执行状态
  paused: boolean;
  stopped: boolean;
  nSteps: number;
  consecutiveFailures: number;
  actionResults: ActionResult[];
}
```

## 🔧 配置系统

### AgentOptions配置

```typescript
export const DEFAULT_AGENT_OPTIONS: AgentOptions = {
  maxSteps: 100,                    // 最大步数
  maxActionsPerStep: 10,           // 每步最大动作数
  maxFailures: 3,                  // 最大失败次数
  useVision: false,                // 是否使用视觉
  validateOutput: true,            // 是否验证输出
  planningInterval: 3,             // 规划间隔
  includeAttributes: [...],        // 包含的HTML属性
};
```

## 🎯 架构优势

### 1. **模块化设计**
- 每个Agent职责单一，易于维护和扩展
- 通过接口和抽象类确保一致性

### 2. **类型安全**
- 使用Zod schema确保运行时类型安全
- TypeScript提供编译时类型检查

### 3. **错误处理**
- 多层错误处理机制
- 自动重试和降级策略

### 4. **可扩展性**
- 插件化的Action系统
- 可配置的Prompt模板
- 灵活的消息管理

### 5. **安全性**
- 提示词注入防护
- 内容标签系统
- 权限控制机制

## 🔮 优化建议

### 1. **性能优化**
- 实现Action缓存机制
- 优化DOM状态比较算法
- 减少不必要的浏览器状态获取

### 2. **可靠性提升**
- 增强错误恢复机制
- 实现更智能的重试策略
- 添加更多的验证检查点

### 3. **功能扩展**
- 支持更多浏览器操作
- 添加并行执行能力
- 实现学习和适应机制

这个架构展现了现代AI Agent系统的最佳实践，通过清晰的职责分离、强类型系统和完善的错误处理，构建了一个既强大又可靠的浏览器自动化框架。

---

# nanobrowser Tool系统深度分析

## 🛠️ Tool系统概览

nanobrowser的Tool系统是整个Agent框架的核心执行引擎，它将AI的决策转化为具体的浏览器操作。系统采用了**Action-Based Architecture**，每个Tool都是一个可执行的Action。

```
┌─────────────────────────────────────────────────────────────┐
│                    Tool系统架构                              │
│                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │ ActionSchema│    │   Action    │    │ActionResult │     │
│  │   (定义)    │───▶│   (执行)    │───▶│   (结果)    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
│         │                   │                   │          │
│         ▼                   ▼                   ▼          │
│  ┌─────────────────────────────────────────────────────────┤
│  │              ActionBuilder (工厂)                       │
│  │  ┌─────────────────────────────────────────────────────┤
│  │  │         NavigatorActionRegistry (注册表)            │
│  │  │  ┌─────────────────────────────────────────────────┤
│  │  │  │              BrowserContext                     │
│  │  │  │  ┌─────────────────────────────────────────────┤
│  │  │  │  │                Page                         │
│  │  │  │  │  ┌─────────────────────────────────────────┤
│  │  │  │  │  │            DOM Service                  │
│  └──┴──┴──┴──┴─────────────────────────────────────────────┘
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Tool分类体系

### 1. **导航类Tools**

| Tool名称 | 功能描述 | 参数 | 实现方式 |
|----------|----------|------|----------|
| `go_to_url` | 导航到指定URL | `url`, `intent` | `BrowserContext.navigateTo()` |
| `go_back` | 返回上一页 | `intent` | `Page.goBack()` |
| `search_google` | Google搜索 | `query`, `intent` | 导航到Google搜索URL |

### 2. **元素交互类Tools**

| Tool名称 | 功能描述 | 参数 | 实现方式 |
|----------|----------|------|----------|
| `click_element` | 点击页面元素 | `index`, `intent`, `xpath?` | `Page.clickElementNode()` |
| `input_text` | 输入文本 | `index`, `text`, `intent`, `xpath?` | `Page.inputText()` |
| `select_dropdown_option` | 选择下拉选项 | `index`, `option`, `intent` | DOM操作 |
| `get_dropdown_options` | 获取下拉选项 | `index`, `intent` | DOM查询 |

### 3. **页面操作类Tools**

| Tool名称 | 功能描述 | 参数 | 实现方式 |
|----------|----------|------|----------|
| `scroll_to_text` | 滚动到文本 | `text`, `intent` | DOM搜索+滚动 |
| `scroll_to_percent` | 滚动到百分比位置 | `percent`, `intent` | `window.scrollTo()` |
| `scroll_to_top` | 滚动到顶部 | `intent` | `window.scrollTo(0, 0)` |
| `scroll_to_bottom` | 滚动到底部 | `intent` | `window.scrollTo(0, document.body.scrollHeight)` |
| `next_page` | 下一页 | `index?`, `intent` | 查找并点击下一页按钮 |
| `previous_page` | 上一页 | `index?`, `intent` | 查找并点击上一页按钮 |

### 4. **标签管理类Tools**

| Tool名称 | 功能描述 | 参数 | 实现方式 |
|----------|----------|------|----------|
| `switch_tab` | 切换标签页 | `tab_id`, `intent` | `chrome.tabs.update()` |
| `open_tab` | 打开新标签页 | `url`, `intent` | `chrome.tabs.create()` |
| `close_tab` | 关闭标签页 | `tab_id`, `intent` | `chrome.tabs.remove()` |

### 5. **键盘操作类Tools**

| Tool名称 | 功能描述 | 参数 | 实现方式 |
|----------|----------|------|----------|
| `send_keys` | 发送按键 | `keys`, `intent` | `Page.sendKeys()` |

### 6. **任务控制类Tools**

| Tool名称 | 功能描述 | 参数 | 实现方式 |
|----------|----------|------|----------|
| `done` | 完成任务 | `text`, `success` | 设置任务完成状态 |
| `wait` | 等待 | `seconds`, `intent` | `setTimeout()` |
| `cache_content` | 缓存内容 | `intent` | 保存页面内容到内存 |

## 🏗️ Tool实现架构

### 1. **ActionSchema - Tool定义层**

```typescript
export interface ActionSchema {
  name: string;           // Tool名称
  description: string;    // Tool描述
  schema: z.ZodType;     // 参数验证schema
}

// 示例：点击元素Tool
export const clickElementActionSchema: ActionSchema = {
  name: 'click_element',
  description: 'Click element by index',
  schema: z.object({
    intent: z.string().default('').describe('purpose of this action'),
    index: z.number().int().describe('index of the element'),
    xpath: z.string().nullable().optional().describe('xpath of the element'),
  }),
};
```

### 2. **Action - Tool执行层**

```typescript
export class Action {
  constructor(
    private readonly handler: (input: any) => Promise<ActionResult>,
    public readonly schema: ActionSchema,
    public readonly hasIndex: boolean = false,
  ) {}

  async call(input: unknown): Promise<ActionResult> {
    // 1. 参数验证
    const parsedArgs = this.schema.schema.safeParse(input);
    if (!parsedArgs.success) {
      throw new InvalidInputError(parsedArgs.error.message);
    }

    // 2. 执行处理函数
    return await this.handler(parsedArgs.data);
  }

  // 索引参数处理（用于DOM元素定位）
  getIndexArg(args: any): number | null
  setIndexArg(args: any, index: number): void
}
```

### 3. **ActionBuilder - Tool工厂**

```typescript
export class ActionBuilder {
  constructor(
    private readonly context: AgentContext,
    private readonly extractorLLM: BaseChatModel
  ) {}

  buildDefaultActions(): Action[] {
    const actions = [];

    // 构建点击元素Tool
    const clickElement = new Action(
      async (input) => {
        // 1. 事件发射
        this.context.emitEvent(Actors.NAVIGATOR, ExecutionState.ACT_START, intent);

        // 2. 获取页面和元素
        const page = await this.context.browserContext.getCurrentPage();
        const state = await page.getState();
        const elementNode = state?.selectorMap.get(input.index);

        // 3. 执行点击操作
        await page.clickElementNode(this.context.options.useVision, elementNode);

        // 4. 返回结果
        return new ActionResult({ extractedContent: msg, includeInMemory: true });
      },
      clickElementActionSchema,
      true // hasIndex = true
    );

    actions.push(clickElement);
    return actions;
  }
}
```

## 🌐 浏览器操作层实现

### 1. **BrowserContext - 浏览器上下文管理**

```typescript
export default class BrowserContext {
  private _attachedPages: Map<number, Page> = new Map();
  private _currentTabId: number | null = null;

  // 核心方法
  async getCurrentPage(): Promise<Page>           // 获取当前页面
  async navigateTo(url: string): Promise<void>    // 导航到URL
  async switchTab(tabId: number): Promise<Page>   // 切换标签页
  async openTab(url: string): Promise<Page>       // 打开新标签页
  async getState(useVision = false): Promise<BrowserState> // 获取浏览器状态
}
```

### 2. **Page - 页面操作层**

```typescript
export default class Page {
  private _puppeteerPage: PuppeteerPage | null = null;
  private _state: PageState;

  // 核心操作方法
  async clickElementNode(useVision: boolean, elementNode: DOMElementNode): Promise<void>
  async inputText(elementNode: DOMElementNode, text: string): Promise<void>
  async navigateTo(url: string): Promise<void>
  async takeScreenshot(fullPage = false): Promise<string | null>
  async getState(useVision = false): Promise<PageState>

  // 元素定位
  async locateElement(element: DOMElementNode): Promise<ElementHandle | null>
}
```

## 🎯 DOM操作系统

### 1. **DOM树构建**

```typescript
// 注入到页面的JavaScript函数
window.buildDomTree = (args) => {
  // 1. 遍历DOM树
  // 2. 识别可交互元素
  // 3. 生成高亮索引
  // 4. 计算元素位置信息
  // 5. 返回结构化数据
};

// 在background script中调用
const results = await chrome.scripting.executeScript({
  target: { tabId },
  func: args => window.buildDomTree(args),
  args: [{ showHighlightElements, focusHighlightIndex, viewportExpansion }],
});
```

### 2. **元素定位策略**

```typescript
async locateElement(element: DOMElementNode): Promise<ElementHandle | null> {
  // 1. 优先使用CSS选择器
  let elementHandle = await currentFrame.$(cssSelector);

  // 2. 回退到XPath选择器
  if (!elementHandle && element.xpath) {
    const xpathSelector = `::-p-xpath(${element.xpath})`;
    elementHandle = await currentFrame.$(xpathSelector);
  }

  // 3. 检查可见性并滚动到视图
  if (elementHandle) {
    const isHidden = await elementHandle.isHidden();
    if (!isHidden) {
      await this._scrollIntoViewIfNeeded(elementHandle);
    }
  }

  return elementHandle;
}
```

### 3. **元素高亮系统**

```typescript
// DOM元素数据结构
export class DOMElementNode extends DOMBaseNode {
  highlightIndex: number | null = null;  // 高亮索引
  isInteractive: boolean = false;        // 是否可交互
  isInViewport: boolean = false;         // 是否在视口内

  // 生成CSS选择器
  getCssSelector(includeDynamicAttributes = true): string {
    let cssSelector = this.convertSimpleXPathToCssSelector(this.xpath);

    // 添加class属性
    const classValue = this.attributes.class;
    if (classValue && includeDynamicAttributes) {
      const classes = classValue.trim().split(/\s+/);
      for (const className of classes) {
        if (validClassNamePattern.test(className)) {
          cssSelector += `.${className}`;
        }
      }
    }

    return cssSelector;
  }
}
```

## 🔄 Tool执行流程

### 1. **Tool调用链路**

```
AI决策 → NavigatorAgent → doMultiAction → Action.call → BrowserContext → Page → DOM操作
```

### 2. **错误处理机制**

```typescript
async doMultiAction(actions: any[]): Promise<ActionResult[]> {
  const results: ActionResult[] = [];
  let errCount = 0;

  for (const [i, action] of actions.entries()) {
    try {
      // 1. 获取Action实例
      const actionInstance = this.actionRegistry.getAction(actionName);

      // 2. 检查DOM变化
      if (i > 0 && indexArg !== null) {
        const newState = await browserContext.getState();
        if (!newPathHashes.isSubsetOf(cachedPathHashes)) {
          // DOM结构发生变化，停止执行
          break;
        }
      }

      // 3. 执行Action
      const result = await actionInstance.call(actionArgs);
      results.push(result);

    } catch (error) {
      errCount++;
      if (errCount > 3) {
        throw new Error('Too many errors in actions');
      }
      results.push(new ActionResult({ error: errorMessage }));
    }
  }

  return results;
}
```

### 3. **智能索引修复**

```typescript
async updateActionIndices(historyElement, action, currentState) {
  // 1. 在当前DOM树中查找历史元素
  const currentElement = await findHistoryElementInTree(
    historyElement,
    currentState.elementTree
  );

  // 2. 如果索引发生变化，自动更新
  if (currentElement.highlightIndex !== oldIndex) {
    actionInstance.setIndexArg(updatedAction, currentElement.highlightIndex);
    logger.info(`Element moved in DOM, updated index from ${oldIndex} to ${currentElement.highlightIndex}`);
  }

  return updatedAction;
}
```

## 🎨 Tool系统特色

### 1. **类型安全**
- 使用Zod schema验证所有Tool参数
- TypeScript提供编译时类型检查
- 运行时参数验证防止错误

### 2. **动态Schema生成**
```typescript
export function buildDynamicActionSchema(actions: Action[]): z.ZodType {
  let schema = z.object({});
  for (const action of actions) {
    schema = schema.extend({
      [action.name()]: action.schema.schema
        .nullable()
        .optional()
        .describe(action.schema.description),
    });
  }
  return schema;
}
```

### 3. **智能元素定位**
- 多重定位策略：CSS选择器 → XPath → 属性匹配
- 自动滚动到视图
- DOM变化检测和索引修复

### 4. **完整的事件系统**
```typescript
// 每个Tool执行都有完整的事件生命周期
this.context.emitEvent(Actors.NAVIGATOR, ExecutionState.ACT_START, intent);
// ... 执行Tool ...
this.context.emitEvent(Actors.NAVIGATOR, ExecutionState.ACT_OK, result);
```

### 5. **视觉增强**
- 支持截图功能用于视觉AI
- 元素高亮显示
- 视口信息计算

## 🚀 Tool系统优势

| 优势 | 具体体现 |
|------|----------|
| **可扩展性** | 插件化设计，易于添加新Tool |
| **可靠性** | 多重错误处理和重试机制 |
| **智能化** | 自动DOM变化适应 |
| **类型安全** | 完整的类型验证体系 |
| **性能优化** | 智能缓存和批量操作 |
| **调试友好** | 完整的日志和事件系统 |

这个Tool系统展现了现代浏览器自动化的最高水准，通过精心设计的分层架构和智能化的执行机制，实现了既强大又可靠的浏览器操作能力。
```
```
