# LangGraph + Stagehand 重构nanobrowser方案分析

## 🎯 重构动机

基于前面的分析，nanobrowser当前架构存在的核心问题：
- **工作流死板**：硬编码的三Agent模式，线性执行流程
- **扩展性差**：编译时确定组件，无法动态适配
- **兼容性问题**：结构化输出在OpenAI兼容API上的问题

## 🚀 LangGraph + Stagehand 方案优势

### LangGraph的核心优势

#### 1. **真正的多Agent协作框架**
```typescript
// 灵活的Agent定义和协作
const workflow = new StateGraph(AgentState)
  .addNode("planner", plannerAgent)
  .addNode("navigator", navigatorAgent) 
  .addNode("validator", validatorAgent)
  .addNode("analyzer", analyzerAgent)     // 新增分析Agent
  .addNode("recovery", recoveryAgent)     // 新增恢复Agent
  .addConditionalEdges("planner", routePlanner, {
    "simple_task": "navigator",
    "complex_task": "analyzer", 
    "retry_needed": "recovery"
  });
```

#### 2. **状态管理和持久化**
```typescript
// 强大的状态管理
const AgentState = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
  }),
  browserState: Annotation<BrowserState>,
  taskProgress: Annotation<TaskProgress>,
  errorHistory: Annotation<ErrorRecord[]>({
    reducer: (x, y) => x.concat(y),
  }),
  currentStrategy: Annotation<string>,
});

// 内置持久化
const memory = new MemorySaver();
const app = workflow.compile({ checkpointer: memory });
```

#### 3. **条件分支和动态路由**
```typescript
// 智能路由决策
function routeBasedOnComplexity(state: typeof AgentState.State) {
  const task = state.currentTask;
  const errorCount = state.errorHistory.length;
  
  if (errorCount > 3) return "recovery_agent";
  if (task.complexity === "high") return "multi_step_planner";
  if (task.type === "data_extraction") return "extraction_specialist";
  return "standard_navigator";
}
```

### Stagehand的核心优势

#### 1. **现代化的浏览器自动化**
```typescript
// 简洁的API设计
const page = stagehand.page;
await page.goto("https://example.com");

// AI驱动的操作
await page.act("click on the login button");
await page.act("fill in the username field with '<EMAIL>'");

// 结构化数据提取
const data = await page.extract({
  instruction: "extract product information",
  schema: z.object({
    name: z.string(),
    price: z.number(),
    availability: z.boolean(),
  }),
});
```

#### 2. **Computer Use模型集成**
```typescript
// 一行代码集成最新的Computer Use模型
const agent = stagehand.agent({
  provider: "openai",
  model: "computer-use-preview",
});
await agent.execute("Navigate to the checkout page and complete the purchase");
```

#### 3. **混合编程模式**
```typescript
// 代码 + 自然语言的完美结合
await page.goto("https://ecommerce.com");           // 精确的代码控制
await page.act("search for wireless headphones");   // AI理解和执行
await page.click('[data-testid="add-to-cart"]');   // 精确的选择器
await page.act("proceed to checkout");              // AI处理复杂流程
```

## 🏗️ 重构架构设计

### 1. **整体架构图**

```
┌─────────────────────────────────────────────────────────────┐
│                    LangGraph Orchestrator                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Planner   │  │  Navigator  │  │  Validator  │         │
│  │   Agent     │  │   Agent     │  │   Agent     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│         │                │                │                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Analyzer   │  │  Recovery   │  │  Extractor  │         │
│  │   Agent     │  │   Agent     │  │   Agent     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│                          │                                 │
│  ┌─────────────────────────────────────────────────────────┤
│  │              Stagehand Browser Engine                   │
│  │  • Computer Use Models  • Mixed Programming             │
│  │  • Smart Element Detection  • Action Caching           │
│  └─────────────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────────────┘
```

### 2. **Agent重新设计**

#### **PlannerAgent 2.0**
```typescript
const plannerAgent = async (state: typeof AgentState.State) => {
  const { messages, browserState, taskProgress } = state;
  
  // 使用LangGraph的条件逻辑
  const complexity = await analyzeTaskComplexity(messages);
  const strategy = await selectStrategy(complexity, taskProgress);
  
  return {
    currentStrategy: strategy,
    nextSteps: await generateSteps(strategy),
    estimatedDifficulty: complexity,
  };
};
```

#### **NavigatorAgent 2.0**
```typescript
const navigatorAgent = async (state: typeof AgentState.State) => {
  const { currentStrategy, nextSteps, browserState } = state;
  
  // 使用Stagehand执行操作
  const page = stagehand.page;
  
  if (currentStrategy === "ai_driven") {
    // 使用Computer Use模型
    const agent = stagehand.agent({
      provider: "openai", 
      model: "computer-use-preview"
    });
    const result = await agent.execute(nextSteps.join(". "));
    return { actionResults: [result] };
  } else {
    // 使用精确的代码控制
    const results = [];
    for (const step of nextSteps) {
      if (step.type === "click") {
        await page.click(step.selector);
      } else if (step.type === "ai_action") {
        await page.act(step.instruction);
      }
      results.push({ step, success: true });
    }
    return { actionResults: results };
  }
};
```

### 3. **动态工作流编排**

```typescript
// 根据任务类型动态构建工作流
function buildWorkflowForTask(taskType: string): StateGraph {
  const workflow = new StateGraph(AgentState);
  
  // 基础节点
  workflow
    .addNode("planner", plannerAgent)
    .addNode("navigator", navigatorAgent)
    .addNode("validator", validatorAgent);
  
  // 根据任务类型添加专门的Agent
  if (taskType === "data_extraction") {
    workflow.addNode("extractor", extractorAgent);
  }
  
  if (taskType === "complex_automation") {
    workflow.addNode("analyzer", analyzerAgent);
    workflow.addNode("recovery", recoveryAgent);
  }
  
  // 动态路由逻辑
  workflow.addConditionalEdges("planner", (state) => {
    if (state.estimatedDifficulty > 0.8) return "analyzer";
    return "navigator";
  });
  
  return workflow;
}
```

### 4. **状态管理升级**

```typescript
// 丰富的状态定义
const AgentState = Annotation.Root({
  // 消息历史
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
  }),
  
  // 浏览器状态
  browserState: Annotation<{
    url: string;
    title: string;
    elements: ElementInfo[];
    screenshot?: string;
  }>,
  
  // 任务进度
  taskProgress: Annotation<{
    completed: string[];
    current: string;
    remaining: string[];
    success_rate: number;
  }>,
  
  // 错误处理
  errorHistory: Annotation<ErrorRecord[]>({
    reducer: (x, y) => x.concat(y),
  }),
  
  // 策略选择
  currentStrategy: Annotation<"ai_driven" | "code_driven" | "hybrid">,
  
  // 缓存的操作
  cachedActions: Annotation<CachedAction[]>({
    reducer: (x, y) => [...x, ...y],
  }),
});
```

## 🎯 具体实施步骤

### Phase 1: 基础迁移 (2-3周)
1. **设置LangGraph环境**
   - 安装LangGraph.js依赖
   - 配置状态管理和持久化
   
2. **集成Stagehand**
   - 替换Puppeteer为Stagehand
   - 迁移基础浏览器操作

3. **重构BaseAgent**
   - 转换为LangGraph节点函数
   - 保持现有接口兼容性

### Phase 2: 工作流重构 (3-4周)
1. **实现动态工作流**
   - 条件路由逻辑
   - 多Agent协作模式
   
2. **状态管理升级**
   - 丰富状态定义
   - 实现持久化

3. **错误处理增强**
   - 智能重试机制
   - 恢复策略

### Phase 3: 高级功能 (2-3周)
1. **Computer Use集成**
   - OpenAI/Anthropic模型
   - 混合编程模式
   
2. **性能优化**
   - 操作缓存
   - 并行执行
   
3. **扩展性增强**
   - 插件系统
   - 自定义Agent

## 📊 对比分析

| 维度 | 当前nanobrowser | LangGraph + Stagehand |
|------|-----------------|------------------------|
| **工作流灵活性** | 固定线性流程 | 动态条件分支 |
| **Agent扩展** | 编译时确定 | 运行时注册 |
| **状态管理** | 简单对象 | 强类型持久化 |
| **错误恢复** | 基础重试 | 智能恢复策略 |
| **浏览器操作** | Puppeteer包装 | 现代AI驱动 |
| **并行能力** | 不支持 | 原生支持 |
| **调试能力** | 基础日志 | 可视化图形 |
| **学习曲线** | 中等 | 中等偏高 |

## 🚀 预期收益

### 1. **灵活性提升**
- 支持复杂的多Agent协作模式
- 动态工作流适配不同任务类型
- 条件分支和智能路由

### 2. **可靠性增强**
- 强大的错误恢复机制
- 状态持久化和回滚能力
- 智能重试策略

### 3. **性能优化**
- 并行Agent执行
- 操作缓存和优化
- Computer Use模型的高效率

### 4. **开发体验**
- 现代化的API设计
- 可视化调试工具
- 更好的类型安全

## 🎯 结论

LangGraph + Stagehand的重构方案将彻底解决nanobrowser当前的架构限制，提供：

1. **真正的多Agent框架**：而不是硬编码的三Agent模式
2. **现代化的浏览器自动化**：AI驱动 + 精确控制的混合模式
3. **企业级的可靠性**：状态持久化、错误恢复、智能重试
4. **无限的扩展性**：插件化架构、动态工作流、运行时配置

这个重构不仅解决了当前问题，更为未来的AI Agent发展奠定了坚实基础。
