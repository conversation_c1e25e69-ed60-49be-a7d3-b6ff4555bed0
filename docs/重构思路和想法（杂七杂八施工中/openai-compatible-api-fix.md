# OpenAI兼容API结构化输出修复

## 问题描述

当使用OpenAI兼容API时，可能会遇到以下错误：
```
Planning failed: Failed to invoke [model-name] with structured output: TypeError: Cannot read properties of undefined (reading 'map')
```

## 根本原因

经过深入分析LangChain源码，发现问题的根本原因是：

1. **LangChain的自动方法选择逻辑**：对于非标准GPT模型名（如`gpt-5-nano`），LangChain自动选择`jsonSchema`方法
2. **JSON Schema功能要求**：`jsonSchema`方法使用OpenAI的新JSON Schema功能，需要特定的响应格式
3. **API兼容性问题**：大多数OpenAI兼容API不支持JSON Schema功能，导致响应格式不匹配
4. **解析失败**：LangChain在解析响应时找不到预期的字段，导致`Cannot read properties of undefined`错误

### LangChain源码分析

在`@langchain/openai`的`chat_models.js`中：

```javascript
// 第2445-2450行
if (!this.model.startsWith("gpt-3") &&
    !this.model.startsWith("gpt-4-") &&
    this.model !== "gpt-4") {
    if (method === undefined) {
        method = "jsonSchema";  // 这里是问题所在！
    }
}
```

对于非标准模型名，LangChain默认使用`jsonSchema`方法，但大多数OpenAI兼容API不支持这个功能。

## 根本解决方案

我们通过强制使用`functionCalling`方法来解决这个问题：

### 1. 强制方法选择

在 `chrome-extension/src/background/agent/agents/base.ts` 中：

```typescript
// 强制使用functionCalling方法，避免JSON Schema问题
const structuredLlm = this.chatLLM.withStructuredOutput(this.modelOutputSchema, {
  includeRaw: true,
  name: this.modelOutputToolName,
  method: "functionCalling", // 强制使用function calling而不是jsonSchema
});
```

### 2. 为什么这样解决

- **Function Calling是标准功能**：所有OpenAI兼容API都支持function calling
- **避免JSON Schema依赖**：不依赖OpenAI的新JSON Schema功能
- **保持完整功能**：结构化输出功能完全保留
- **兼容性最佳**：适用于所有OpenAI兼容API

### 3. 支持的API类型

- ✅ 标准OpenAI API
- ✅ Azure OpenAI
- ✅ OpenAI兼容API（如你的上游API）
- ✅ 其他自定义OpenAI兼容服务

## 测试验证

修复后，你的OpenAI兼容API应该能够：

1. **正常处理聊天请求**
2. **自动处理结构化输出**
3. **在遇到兼容性问题时优雅降级**
4. **保持完整的功能性**

## 使用说明

1. **刷新扩展**：在 `chrome://extensions/` 中刷新nanobrowser扩展
2. **重新测试**：使用你的OpenAI兼容API进行测试
3. **查看日志**：如果仍有问题，检查浏览器控制台的详细日志

## 技术细节

### 错误检测
- 特定检测 `Cannot read properties of undefined (reading 'map')` 错误
- 只对这种特定错误进行回退，其他错误正常抛出

### 回退机制
- 自动切换到手动JSON提取模式
- 使用正则表达式和JSON解析
- 保持相同的输出格式

### 性能影响
- 几乎无性能影响
- 只在遇到错误时才触发回退
- 成功的请求保持原有性能
