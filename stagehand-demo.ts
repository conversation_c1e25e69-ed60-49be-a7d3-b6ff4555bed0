import { Stagehand } from "@browserbasehq/stagehand";
import { z } from "zod";

async function main() {
  console.log("🎭 Starting Stagehand demo...");

  // 初始化 Stagehand 实例
  const stagehand = new Stagehand({
    env: "LOCAL", // 使用本地浏览器
    verbose: 1, // 启用详细日志
    enableCaching: false, // 禁用缓存以便测试
    localBrowserLaunchOptions: {
      headless: false, // 显示浏览器窗口，方便观察
      viewport: {
        width: 1280,
        height: 720,
      },
    },
  });

  try {
    // 初始化浏览器
    await stagehand.init();
    console.log("✅ Stagehand initialized successfully!");

    const page = stagehand.page;

    // 示例 1: 访问网站并观察页面
    console.log("\n📍 Step 1: Navigating to Google...");
    await page.goto("https://www.google.com");
    
    // 观察页面上可以执行的操作
    console.log("🔍 Observing page actions...");
    const actions = await page.observe({
      instruction: "Find the search input and search button"
    });
    console.log("Found actions:", actions.slice(0, 3)); // 只显示前3个

    // 示例 2: 执行搜索操作
    console.log("\n🔍 Step 2: Performing search...");
    await page.act("Type 'Stagehand browser automation' in the search box");
    await page.act("Click the search button or press Enter");

    // 等待搜索结果加载
    await page.waitForTimeout(2000);

    // 示例 3: 提取搜索结果
    console.log("\n📊 Step 3: Extracting search results...");
    const searchResults = await page.extract({
      instruction: "Extract the first 3 search result titles and URLs",
      schema: z.object({
        results: z.array(z.object({
          title: z.string(),
          url: z.string().optional(),
        })).max(3)
      }),
    });

    console.log("🎯 Search Results:");
    searchResults.results.forEach((result, index) => {
      console.log(`${index + 1}. ${result.title}`);
      if (result.url) console.log(`   URL: ${result.url}`);
    });

    // 示例 4: 访问另一个网站
    console.log("\n📍 Step 4: Navigating to GitHub...");
    await page.goto("https://github.com");

    // 观察 GitHub 首页
    const githubActions = await page.observe({
      instruction: "Find the sign in button and search box"
    });
    console.log("GitHub actions found:", githubActions.slice(0, 2));

    // 提取 GitHub 首页信息
    const githubInfo = await page.extract({
      instruction: "Extract the main heading and description from the page",
      schema: z.object({
        heading: z.string(),
        description: z.string().optional(),
      }),
    });

    console.log("🐙 GitHub Info:");
    console.log(`Heading: ${githubInfo.heading}`);
    if (githubInfo.description) {
      console.log(`Description: ${githubInfo.description}`);
    }

    console.log("\n🎉 Demo completed successfully!");

  } catch (error) {
    console.error("❌ Error during demo:", error);
  } finally {
    // 清理资源
    console.log("\n🧹 Cleaning up...");
    await stagehand.close();
    console.log("✅ Stagehand closed successfully!");
  }
}

// 运行示例
main().catch(console.error);
