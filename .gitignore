# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.cache/
.parcel-cache/
.vite/
*.tsbuildinfo
.tsbuild/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.idea/
.vscode/
*.code-workspace
*.sublime-project
*.sublime-workspace
.history/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini
$RECYCLE.BIN/

# Testing and coverage
coverage/
*.lcov
.nyc_output/
junit.xml
test-results/
playwright-report/

# Linting and formatting cache
.eslintcache
.prettiercache
.stylelintcache

# Chrome Extension
*.crx
*.pem
key.pem

# Package managers (choose one based on your preference)
# package-lock.json  # if using yarn
# yarn.lock          # if using npm

# Temporary files
*.tmp
*.temp
.tmp/
logs/
*.log

# Optional npm cache directory
.npm

# Misc
.settings/
