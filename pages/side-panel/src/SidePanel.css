/* Add this at the top of the file */
body {
  border-color: rgb(159, 6, 11) !important; /* sky-200 color */
}

/* Remove unused classes: .App, .App-logo, .App-header */

code {
  background: rgba(186, 230, 253, 0.4);
  border-radius: 0.25rem;
  padding: 0.2rem 0.5rem;
  color: #0369a1;
}

/* Dark mode support for code */
@media (prefers-color-scheme: dark) {
  code {
    background: rgba(30, 58, 138, 0.4);
    color: #7dd3fc;
  }
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #0ea5e9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0284c7;
}

/* Dark mode scrollbar */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-track {
    background: #1e293b;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #0ea5e9;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #38bdf8;
  }
}

/* Used classes from the component */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: transparent;
  border-bottom: 1px solid rgba(14, 165, 233, 0.2);
}

/* Dark mode header border */
@media (prefers-color-scheme: dark) {
  .header {
    border-bottom: 1px solid rgba(14, 165, 233, 0.3);
  }
}

.header-logo {
  display: flex;
  align-items: center;
}

.header-icons {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.header-icon {
  color: #19C2FF;
  cursor: pointer;
  transition: color 0.2s ease;
  opacity: 0.8;
}

.header-icon:hover {
  color: #0073DC;
  opacity: 1;
}

.send-button {
  color: #19C2FF;
  cursor: pointer;
  transition: color 0.2s ease;
  opacity: 0.8;
}

.send-button:hover {
  color: #0073DC;
  opacity: 1;
}

.send-button:disabled {
  color: #CBD5E1;
  cursor: not-allowed;
  opacity: 0.5;
}

/* Add these styles to your existing CSS */
.scrollbar-gutter-stable {
  scrollbar-gutter: stable;
}

/* Optional: Style the scrollbar for webkit browsers */
.scrollbar-gutter-stable::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-gutter-stable::-webkit-scrollbar-track {
  background: #f1f5f9;  /* sky-50 */
}

.scrollbar-gutter-stable::-webkit-scrollbar-thumb {
  background: #7dd3fc;  /* sky-300 - very light blue */
  border-radius: 4px;
}

.scrollbar-gutter-stable::-webkit-scrollbar-thumb:hover {
  background: #38bdf8;  /* sky-400 - slightly darker on hover */
}

/* Dark mode scrollbar for scrollbar-gutter-stable */
@media (prefers-color-scheme: dark) {
  .scrollbar-gutter-stable::-webkit-scrollbar-track {
    background: #1e293b;  /* slate-800 */
  }
  
  .scrollbar-gutter-stable::-webkit-scrollbar-thumb {
    background: #0ea5e9;  /* sky-500 */
  }
  
  .scrollbar-gutter-stable::-webkit-scrollbar-thumb:hover {
    background: #38bdf8;  /* sky-400 */
  }
}

/* Dark mode text and background colors */
@media (prefers-color-scheme: dark) {
  .dark-mode-text {
    color: #e2e8f0 !important; /* slate-200 */
  }
  
  .dark-mode-bg {
    background-color: #1e293b !important; /* slate-800 */
  }
  
  .dark-mode-border {
    border-color: #475569 !important; /* slate-600 */
  }
}
