#!/bin/bash

# Stagehand with Gemini 2.0 Flash
# ===============================

echo "🚀 Starting Stagehand with Gemini 2.0 Flash"
echo "============================================"

# 设置代理环境变量
export https_proxy=http://127.0.0.1:7897
export http_proxy=http://127.0.0.1:7897
export all_proxy=socks5://127.0.0.1:7897

echo "✅ Proxy configured for Gemini API access:"
echo "   https_proxy=$https_proxy"
echo "   http_proxy=$http_proxy"
echo "   all_proxy=$all_proxy"
echo ""

# 检查 Gemini API Key
if [ -z "$GOOGLE_API_KEY" ]; then
    echo "🔍 Checking .env file for Gemini API key..."
    if [ -f ".env" ] && grep -q "GOOGLE_API_KEY=" .env; then
        echo "✅ Found Gemini API key in .env file"
    else
        echo "⚠️  No Gemini API key found!"
        echo "💡 Please add your key to .env file:"
        echo "   GOOGLE_API_KEY=your-key-here"
        echo ""
    fi
else
    echo "✅ Gemini API key found in environment"
fi

# 检查代理连接
echo "🔍 Testing proxy connection..."
if curl -s --proxy $https_proxy --max-time 5 https://www.google.com > /dev/null; then
    echo "✅ Proxy connection successful!"
else
    echo "⚠️  Proxy connection failed"
    echo "💡 Please ensure your proxy is running on 127.0.0.1:7897"
    echo "🔧 You can still use basic commands without proxy"
fi

echo ""
echo "🚀 Starting Gemini 2.0 Flash Control..."
echo "🌟 Enhanced natural language processing available!"
echo "💡 Try commands like:"
echo "   'Navigate to the GitHub homepage'"
echo "   'Search for machine learning tutorials'"
echo "   'Extract all the article titles'"
echo ""

# 启动 Gemini 2.0 Flash 控制
npm run gemini-pro
