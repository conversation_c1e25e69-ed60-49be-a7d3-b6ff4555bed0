#!/usr/bin/env tsx

import { createStagehand, check<PERSON><PERSON><PERSON><PERSON>, getApi<PERSON>eyInstructions } from './config.js';
import { z } from 'zod';
import path from 'path';

async function aiDemo() {
  console.log("🤖 Stagehand AI Demo");
  console.log("====================");
  console.log("This demo shows AI-powered browser automation.\n");

  // 检查 API key
  if (!checkApiKey()) {
    console.log("❌ No API key found!");
    console.log(getApiKeyInstructions());
    return;
  }

  const stagehand = createStagehand();

  try {
    await stagehand.init();
    console.log("✅ Stagehand with AI initialized!");

    const page = stagehand.page;
    const testHtmlPath = path.join(process.cwd(), "test.html");
    const fileUrl = `file://${testHtmlPath}`;

    console.log("\n📍 Step 1: Loading test page...");
    await page.goto(fileUrl);
    await page.waitForTimeout(2000);

    console.log("\n🔍 Step 2: AI observing the page...");
    const observations = await page.observe({
      instruction: "Find all interactive elements on this page"
    });
    
    console.log("🤖 AI found these elements:");
    observations.slice(0, 4).forEach((obs, index) => {
      console.log(`${index + 1}. ${obs.description}`);
    });

    console.log("\n🎯 Step 3: AI performing actions...");
    await page.act("Type 'AI-powered test' in the search input field");
    await page.act("Click the search button");
    await page.waitForTimeout(1000);

    console.log("\n📊 Step 4: AI extracting information...");
    const pageInfo = await page.extract({
      instruction: "Extract the page title, search input value, and search result text",
      schema: z.object({
        title: z.string(),
        searchValue: z.string(),
        resultText: z.string(),
      }),
    });

    console.log("🤖 AI extracted:");
    console.log(`📄 Title: ${pageInfo.title}`);
    console.log(`🔍 Search: ${pageInfo.searchValue}`);
    console.log(`📝 Result: ${pageInfo.resultText}`);

    console.log("\n🧹 Step 5: AI clearing and testing again...");
    await page.act("Click the clear button");
    await page.waitForTimeout(500);
    
    await page.act("Type 'Stagehand rocks!' in the search box");
    await page.act("Click the search button");
    await page.waitForTimeout(1000);

    const finalResult = await page.extract({
      instruction: "Get the current search result text",
      schema: z.object({
        result: z.string(),
      }),
    });

    console.log(`🎉 Final AI result: ${finalResult.result}`);

    console.log("\n✨ AI demo completed successfully!");
    console.log("🚀 Try more complex scenarios by modifying this script!");

  } catch (error) {
    console.error("❌ Error:", error);
    if (error.message?.includes('API key')) {
      console.log(getApiKeyInstructions());
    }
  } finally {
    await stagehand.close();
    console.log("✅ Demo finished!");
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  aiDemo().catch(console.error);
}
