#!/usr/bin/env tsx

import { Stagehand } from '@browserbasehq/stagehand';
import { z } from 'zod';
import { config } from 'dotenv';

// 加载环境变量
config();

async function testYourApi() {
  console.log("🧪 Testing Your Gemini 2.5 Flash Preview API");
  console.log("==============================================");
  
  console.log("📋 Configuration:");
  console.log(`🔑 API Key: ${process.env.OPENAI_API_KEY?.substring(0, 20)}...`);
  console.log(`🌐 Base URL: ${process.env.OPENAI_BASE_URL}`);
  console.log("");

  // 测试不同的配置方式
  const configs = [
    {
      name: "Standard OpenAI Compatible",
      config: {
        env: "LOCAL" as const,
        verbose: 1,
        enableCaching: false,
        modelName: "gpt-4o-mini", // 使用标准模型名
        modelClientOptions: {
          apiKey: process.env.OPENAI_API_KEY,
          baseURL: process.env.OPENAI_BASE_URL,
        },
        localBrowserLaunchOptions: {
          headless: true, // 无头模式测试
          viewport: { width: 1280, height: 720 },
        },
      }
    },
    {
      name: "Custom Model Name",
      config: {
        env: "LOCAL" as const,
        verbose: 1,
        enableCaching: false,
        modelName: "gemini-2.5-flash-preview-05-20",
        modelClientOptions: {
          apiKey: process.env.OPENAI_API_KEY,
          baseURL: process.env.OPENAI_BASE_URL,
        },
        localBrowserLaunchOptions: {
          headless: true,
          viewport: { width: 1280, height: 720 },
        },
      }
    }
  ];

  for (const testConfig of configs) {
    console.log(`\n🧪 Testing: ${testConfig.name}`);
    console.log("=" + "=".repeat(testConfig.name.length + 10));
    
    let stagehand;
    try {
      stagehand = new Stagehand(testConfig.config);
      await stagehand.init();
      console.log("✅ Stagehand initialization: SUCCESS");

      const page = stagehand.page;
      
      // 测试基础导航
      console.log("🌐 Testing basic navigation...");
      await page.goto("https://example.com");
      const title = await page.title();
      console.log(`✅ Navigation: SUCCESS - Title: ${title}`);

      // 测试 AI 功能
      console.log("🤖 Testing AI extract function...");
      try {
        const result = await page.extract({
          instruction: "Get the main heading of this page",
          schema: z.object({
            heading: z.string(),
          }),
        });
        console.log(`✅ AI Extract: SUCCESS - Heading: ${result.heading}`);
      } catch (aiError) {
        console.log(`❌ AI Extract: FAILED - ${aiError.message}`);
      }

      // 测试 AI 观察功能
      console.log("👁️ Testing AI observe function...");
      try {
        const observations = await page.observe({
          instruction: "Find interactive elements on this page"
        });
        console.log(`✅ AI Observe: SUCCESS - Found ${observations.length} elements`);
        if (observations.length > 0) {
          console.log(`   First element: ${observations[0].description}`);
        }
      } catch (aiError) {
        console.log(`❌ AI Observe: FAILED - ${aiError.message}`);
      }

      // 测试 AI 动作功能
      console.log("🎯 Testing AI act function...");
      try {
        await page.act("scroll to the bottom of the page");
        console.log("✅ AI Act: SUCCESS - Scroll action completed");
      } catch (aiError) {
        console.log(`❌ AI Act: FAILED - ${aiError.message}`);
      }

      await stagehand.close();
      console.log("✅ Cleanup: SUCCESS");

    } catch (error) {
      console.log(`❌ ${testConfig.name}: FAILED - ${error.message}`);
      if (stagehand) {
        try {
          await stagehand.close();
        } catch (closeError) {
          // Ignore cleanup errors
        }
      }
    }
  }

  console.log("\n📊 Test Summary");
  console.log("===============");
  console.log("If any configuration worked, you can use that setup.");
  console.log("If all failed, there might be an issue with:");
  console.log("1. API key validity");
  console.log("2. Base URL accessibility");
  console.log("3. Model name compatibility");
  console.log("4. API service availability");
  
  console.log("\n💡 Recommended next steps:");
  console.log("1. Try the working configuration in the main control script");
  console.log("2. Check your API service documentation for correct model names");
  console.log("3. Verify your API credits/quota");
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testYourApi().catch(console.error);
}
