{"name": "stagehand-demo", "version": "1.0.0", "description": "Stagehand browser automation demo for CUTE project", "type": "module", "scripts": {"install-browsers": "playwright install", "basic": "tsx basic-demo.ts", "ai": "tsx ai-demo.ts", "local": "tsx local-demo.ts", "examples": "tsx examples.ts", "enhanced": "tsx enhanced-demo.ts", "interactive": "tsx interactive-control.ts", "control": "tsx interactive-control.ts", "gemini": "tsx gemini-control.ts", "natural": "tsx gemini-control.ts", "gemini-pro": "tsx gemini-pro-control.ts", "gemini-2": "tsx gemini-pro-control.ts", "openai-compat": "tsx openai-compatible-control.ts", "compat": "tsx openai-compatible-control.ts", "custom": "tsx custom-api-control.ts", "api": "tsx custom-api-control.ts", "gemini-25": "tsx gemini-25-control.ts", "g25": "tsx gemini-25-control.ts", "test-api": "tsx test-your-api.ts", "test-ai": "tsx test-ai-local.ts", "debug": "tsx debug-config.ts", "setup": "npm install && npm run install-browsers", "test": "tsx test-all.ts"}, "dependencies": {"@browserbasehq/stagehand": "^2.4.2", "playwright": "^1.54.2", "zod": "^3.25.58"}, "devDependencies": {"tsx": "^4.19.2", "@types/node": "^22.5.5", "typescript": "^5.5.4"}, "keywords": ["stagehand", "browser-automation", "ai", "playwright", "web-automation"], "author": "CUTE Project", "license": "Apache-2.0"}