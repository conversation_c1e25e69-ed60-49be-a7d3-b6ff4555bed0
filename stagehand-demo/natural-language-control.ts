#!/usr/bin/env tsx

import { createStagehand, checkApi<PERSON>ey } from './config.js';
import { z } from 'zod';
import * as readline from 'readline';

class NaturalLanguageController {
  private stagehand: any;
  private page: any;
  private rl: readline.Interface;
  private hasApiKey: boolean;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.hasApiKey = checkApiKey();
  }

  async init() {
    console.log("🤖 Stagehand Natural Language Control");
    console.log("=====================================");
    
    if (!this.hasApiKey) {
      console.log("❌ No API key found!");
      console.log("\n🔑 To use natural language features, you need an OpenAI API key:");
      console.log("1. Visit: https://platform.openai.com/api-keys");
      console.log("2. Create an API key");
      console.log("3. Edit .env file and add: OPENAI_API_KEY=sk-your-key-here");
      console.log("4. Restart this script");
      console.log("\n💡 For now, you can still use basic commands like:");
      console.log("   goto https://google.com");
      console.log("   click input[name='q']");
      console.log("   type input[name='q'] hello world");
      console.log("");
    } else {
      console.log("✅ AI Features Available!");
      console.log("🌟 You can now use natural language commands!");
      console.log("");
    }

    this.stagehand = createStagehand();
    await this.stagehand.init();
    this.page = this.stagehand.page;
    
    console.log("✅ Browser initialized and ready!");
    console.log("💬 Start typing your commands...\n");
  }

  private question(prompt: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  private async showHelp() {
    console.log("\n💬 Natural Language Commands (with API key):");
    console.log("============================================");
    console.log("🌐 Navigation:");
    console.log("  'Go to Google'");
    console.log("  'Navigate to GitHub'");
    console.log("  'Visit https://example.com'");
    console.log("  'Go back to previous page'");
    
    console.log("\n🎯 Actions:");
    console.log("  'Click the search button'");
    console.log("  'Type hello world in the search box'");
    console.log("  'Fill the email <NAME_EMAIL>'");
    console.log("  'Click the login button'");
    console.log("  'Scroll down the page'");
    
    console.log("\n📊 Information:");
    console.log("  'What is the page title?'");
    console.log("  'Get all the links on this page'");
    console.log("  'Extract the main content'");
    console.log("  'Find all buttons on the page'");
    
    console.log("\n🔧 Basic Commands (always available):");
    console.log("  goto <url>           - Navigate to URL");
    console.log("  click <selector>     - Click element");
    console.log("  type <selector> <text> - Type text");
    console.log("  screenshot           - Take screenshot");
    console.log("  help                 - Show this help");
    console.log("  exit                 - Close and exit");
    
    console.log("\n💡 Examples:");
    if (this.hasApiKey) {
      console.log("  🤖 'Search for Stagehand on Google'");
      console.log("  🤖 'Click the first search result'");
      console.log("  🤖 'Get the main heading of this page'");
    } else {
      console.log("  🔧 goto google.com");
      console.log("  🔧 click textarea[name='q']");
      console.log("  🔧 type textarea[name='q'] Stagehand");
    }
    console.log("");
  }

  private isBasicCommand(command: string): boolean {
    const basicCommands = ['goto', 'click', 'type', 'fill', 'clear', 'scroll', 'text', 'value', 'visible', 'screenshot', 'wait', 'viewport', 'back', 'forward', 'refresh', 'url', 'title', 'help', 'exit'];
    const firstWord = command.trim().split(' ')[0].toLowerCase();
    return basicCommands.includes(firstWord);
  }

  private async executeBasicCommand(command: string) {
    const parts = command.trim().split(' ');
    const cmd = parts[0].toLowerCase();
    const args = parts.slice(1);

    try {
      switch (cmd) {
        case 'goto':
          if (args.length === 0) {
            console.log("❌ Usage: goto <url>");
            break;
          }
          let url = args.join(' ');
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }
          console.log(`🌐 Navigating to: ${url}`);
          await this.page.goto(url);
          console.log("✅ Navigation completed");
          break;

        case 'click':
          if (args.length === 0) {
            console.log("❌ Usage: click <selector>");
            break;
          }
          const selector = args.join(' ');
          console.log(`🖱️ Clicking: ${selector}`);
          await this.page.click(selector);
          console.log("✅ Clicked");
          break;

        case 'type':
          if (args.length < 2) {
            console.log("❌ Usage: type <selector> <text>");
            break;
          }
          const typeSelector = args[0];
          const typeText = args.slice(1).join(' ');
          console.log(`⌨️ Typing "${typeText}" into: ${typeSelector}`);
          await this.page.type(typeSelector, typeText);
          console.log("✅ Text typed");
          break;

        case 'screenshot':
          const filename = args[0] || `screenshot-${Date.now()}.png`;
          console.log(`📸 Taking screenshot: ${filename}`);
          await this.page.screenshot({ path: filename });
          console.log(`✅ Screenshot saved: ${filename}`);
          break;

        case 'title':
          const title = await this.page.title();
          console.log(`📄 Page title: ${title}`);
          break;

        case 'url':
          console.log(`🔗 Current URL: ${this.page.url()}`);
          break;

        case 'help':
          await this.showHelp();
          break;

        case 'exit':
          console.log("👋 Closing browser and exiting...");
          await this.stagehand.close();
          this.rl.close();
          process.exit(0);
          break;

        default:
          console.log(`❌ Unknown command: ${cmd}`);
          console.log("💡 Type 'help' for available commands");
          break;
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  private async executeNaturalLanguageCommand(command: string) {
    if (!this.hasApiKey) {
      console.log("❌ Natural language commands require an API key.");
      console.log("💡 Use basic commands like: goto google.com");
      return;
    }

    try {
      console.log(`🤖 Processing: "${command}"`);

      // 判断是否是导航命令
      if (command.toLowerCase().includes('go to') || command.toLowerCase().includes('navigate to') || command.toLowerCase().includes('visit')) {
        // 尝试提取URL
        const urlMatch = command.match(/(?:go to|navigate to|visit)\s+(.+)/i);
        if (urlMatch) {
          let url = urlMatch[1].trim();
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            // 如果是常见网站名，自动添加.com
            if (!url.includes('.')) {
              url = url + '.com';
            }
            url = 'https://' + url;
          }
          console.log(`🌐 Navigating to: ${url}`);
          await this.page.goto(url);
          console.log("✅ Navigation completed");
          return;
        }
      }

      // 判断是否是信息查询命令
      if (command.toLowerCase().includes('what is') || command.toLowerCase().includes('get') || command.toLowerCase().includes('extract') || command.toLowerCase().includes('find')) {
        console.log("📊 Extracting information...");
        const result = await this.page.extract({
          instruction: command,
          schema: z.object({
            result: z.string(),
          }),
        });
        console.log(`📋 Result: ${result.result}`);
        return;
      }

      // 其他动作命令
      console.log("🎯 Executing action...");
      await this.page.act(command);
      console.log("✅ Action completed");

    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      console.log("💡 Try a more specific command or use basic syntax");
    }
  }

  async start() {
    await this.init();
    
    while (true) {
      const command = await this.question('💬 > ');
      
      if (command.trim() === '') continue;
      if (command.trim() === 'exit') {
        await this.executeBasicCommand('exit');
        break;
      }

      // 判断是基础命令还是自然语言命令
      if (this.isBasicCommand(command)) {
        await this.executeBasicCommand(command);
      } else {
        await this.executeNaturalLanguageCommand(command);
      }
    }
  }
}

// 启动自然语言控制
if (import.meta.url === `file://${process.argv[1]}`) {
  const controller = new NaturalLanguageController();
  controller.start().catch(console.error);
}
