# 🎭 Stagehand Demo

A comprehensive demo collection for the Stagehand browser automation framework, integrated with the CUTE project.

## 🚀 Quick Start

### 1. Setup
```bash
# Install dependencies and browsers
npm run setup

# Or step by step:
npm install
npm run install-browsers
```

### 2. Basic Demo (No API Key Required)
```bash
npm run basic
```

### 3. AI Demo (Requires API Key)
```bash
# Copy and configure environment
cp .env.example .env
# Edit .env with your API key

# Run AI demo
npm run ai
```

## 📋 Available Scripts

| Script | Description | API Key Required |
|--------|-------------|------------------|
| `npm run basic` | Basic browser automation | ❌ No |
| `npm run ai` | AI-powered automation | ✅ Yes |
| `npm run local` | Test with real websites | ⚠️ Optional |
| `npm run examples` | Multiple example scenarios | ⚠️ Optional |
| `npm test` | Comprehensive test suite | ⚠️ Optional |
| `npm run setup` | Install dependencies | ❌ No |

## 🔑 API Key Setup

### OpenAI (Recommended)
1. Get your API key from [OpenAI Platform](https://platform.openai.com/api-keys)
2. Copy `.env.example` to `.env`
3. Add your key: `OPENAI_API_KEY=sk-your-key-here`

### Anthropic (Alternative)
1. Get your API key from [Anthropic Console](https://console.anthropic.com/)
2. Add to `.env`: `ANTHROPIC_API_KEY=your-key-here`

## 📁 File Structure

```
stagehand-demo/
├── README.md              # This file
├── package.json           # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
├── .env.example           # Environment template
├── config.ts              # Shared configuration
├── test.html              # Test webpage
├── basic-demo.ts          # Basic automation demo
├── ai-demo.ts             # AI-powered demo
├── local-demo.ts          # Real website testing
├── examples.ts            # Multiple examples
└── test-all.ts            # Test suite
```

## 🎯 Demo Features

### Basic Demo
- ✅ Browser initialization
- ✅ Page navigation
- ✅ Form interactions
- ✅ Element clicking
- ✅ Text extraction
- ✅ Dialog handling

### AI Demo
- 🤖 AI page observation
- 🤖 Natural language actions
- 🤖 Structured data extraction
- 🤖 Intelligent element finding

### Examples Collection
- 📝 Form automation
- 🔍 Element discovery
- 📊 Data extraction
- 🔄 Sequential actions

## 🛠️ Configuration

Edit `config.ts` to customize:

```typescript
export const defaultConfig: DemoConfig = {
  headless: false,           // Show browser window
  viewport: {
    width: 1280,
    height: 720,
  },
  verbose: 1,                // Log level
  modelName: 'gpt-4o-mini',  // AI model
};
```

## 🔧 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key | - |
| `ANTHROPIC_API_KEY` | Anthropic API key | - |
| `DEMO_HEADLESS` | Run headless | `false` |
| `DEMO_VIEWPORT_WIDTH` | Browser width | `1280` |
| `DEMO_VIEWPORT_HEIGHT` | Browser height | `720` |
| `DEMO_VERBOSE` | Log verbosity | `1` |

## 🧪 Testing

Run the comprehensive test suite:

```bash
npm test
```

This will test:
- ✅ Basic browser operations
- ✅ Element interactions
- ✅ Text extraction
- ✅ Dialog handling
- 🤖 AI features (if API key available)

## 🚨 Troubleshooting

### Browser Not Found
```bash
npm run install-browsers
```

### Network Issues
The demos will fallback to local files if network access fails.

### API Key Issues
- Ensure your API key is valid
- Check your OpenAI/Anthropic account has credits
- Verify the key is correctly set in `.env`

### Permission Issues
Make sure the scripts are executable:
```bash
chmod +x *.ts
```

## 📚 Learn More

- [Stagehand Documentation](https://docs.stagehand.dev/)
- [Stagehand GitHub](https://github.com/browserbase/stagehand)
- [Playwright Documentation](https://playwright.dev/)

## 🤝 Integration with CUTE

This demo is designed to integrate seamlessly with the CUTE project:

1. **Standalone Operation**: Can run independently
2. **Shared Dependencies**: Uses compatible versions
3. **Configuration**: Easily customizable for different environments
4. **Examples**: Demonstrates patterns useful for AI web automation

## 📝 Next Steps

1. **Run Basic Demo**: Start with `npm run basic`
2. **Set Up API Key**: Configure `.env` for AI features
3. **Explore Examples**: Try `npm run examples`
4. **Customize**: Modify scripts for your use cases
5. **Integrate**: Use patterns in your CUTE project

---

**Happy Automating! 🎭✨**
