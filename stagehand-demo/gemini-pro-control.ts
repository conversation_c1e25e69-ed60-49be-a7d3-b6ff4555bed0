#!/usr/bin/env tsx

import { Stagehand } from '@browserbasehq/stagehand';
import { z } from 'zod';
import * as readline from 'readline';
import { config } from 'dotenv';

// 加载环境变量
config();

class GeminiProController {
  private stagehand: any;
  private page: any;
  private rl: readline.Interface;
  private hasApiKey: boolean;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.hasApiKey = !!process.env.GOOGLE_API_KEY;
  }

  async init() {
    console.log("🚀 Stagehand + Gemini 2.0 Flash Control");
    console.log("========================================");
    console.log(`🔑 Gemini API Key: ${this.hasApiKey ? '✅ Found' : '❌ Not found'}`);
    
    if (!this.hasApiKey) {
      console.log("\n❌ No Gemini API key found!");
      console.log("💡 Please set GOOGLE_API_KEY in .env file");
      console.log("🔧 Falling back to basic commands only");
    } else {
      console.log("✅ Gemini 2.0 Flash Features Available!");
      console.log("🌟 Enhanced natural language processing!");
    }

    try {
      // 创建 Stagehand 实例，明确指定 Gemini 2.0 Flash
      this.stagehand = new Stagehand({
        env: "LOCAL",
        verbose: 1,
        enableCaching: false,
        modelName: "google/gemini-2.0-flash-exp", // 明确使用 Gemini 2.0 Flash
        modelClientOptions: this.hasApiKey ? {
          apiKey: process.env.GOOGLE_API_KEY,
        } : undefined,
        localBrowserLaunchOptions: {
          headless: process.env.DEMO_HEADLESS === 'true',
          viewport: {
            width: parseInt(process.env.DEMO_VIEWPORT_WIDTH || '1280'),
            height: parseInt(process.env.DEMO_VIEWPORT_HEIGHT || '720'),
          },
        },
      });

      await this.stagehand.init();
      this.page = this.stagehand.page;
      
      console.log("✅ Browser initialized with Gemini 2.0 Flash!");
      console.log("💬 Ready for advanced natural language commands...\n");
      
      if (this.hasApiKey) {
        console.log("🎯 Try these enhanced commands:");
        console.log("  'Navigate to the GitHub homepage'");
        console.log("  'Search for artificial intelligence tutorials'");
        console.log("  'Find and click the login button'");
        console.log("  'Extract all the article titles from this page'");
        console.log("  'Take a screenshot and describe what you see'");
        console.log("");
      }
    } catch (error) {
      console.error("❌ Initialization error:", error.message);
      if (error.message.includes('location')) {
        console.log("\n🌍 Gemini API 地区限制解决方案:");
        console.log("1. 确保代理正在运行 (127.0.0.1:7897)");
        console.log("2. 尝试使用 ./start-with-proxy.sh 启动");
        console.log("3. 或者获取 OpenAI API key 作为替代");
      }
    }
  }

  private question(prompt: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  private async showHelp() {
    console.log("\n📖 Gemini 2.0 Flash Commands:");
    console.log("==============================");
    
    if (this.hasApiKey) {
      console.log("🤖 Enhanced Natural Language Commands:");
      console.log("  'Navigate to [website/description]'");
      console.log("  'Search for [detailed query]'");
      console.log("  'Find and click [element description]'");
      console.log("  'Fill [field description] with [content]'");
      console.log("  'Extract [specific information]'");
      console.log("  'Describe what you see on this page'");
      console.log("  'Find all [type of elements]'");
      console.log("  'Scroll to [position/element]'");
      console.log("");
      console.log("💡 Enhanced Examples:");
      console.log("  'Go to the official Python documentation website'");
      console.log("  'Search for machine learning tutorials on this site'");
      console.log("  'Click the button that says Sign Up or Register'");
      console.log("  'Fill the email input <NAME_EMAIL>'");
      console.log("  'Extract all the product names and prices'");
      console.log("  'Find all the navigation menu items'");
      console.log("");
    }
    
    console.log("🔧 Basic Commands (Always Available):");
    console.log("  goto <url>              - Navigate to URL");
    console.log("  click <selector>        - Click element");
    console.log("  type <selector> <text>  - Type text");
    console.log("  title                   - Get page title");
    console.log("  url                     - Get current URL");
    console.log("  screenshot [filename]   - Take screenshot");
    console.log("  wait <ms>              - Wait milliseconds");
    console.log("  help                   - Show this help");
    console.log("  exit                   - Close and exit");
    console.log("");
  }

  private isBasicCommand(command: string): boolean {
    const basicCommands = ['goto', 'click', 'type', 'fill', 'clear', 'title', 'url', 'screenshot', 'wait', 'help', 'exit'];
    const firstWord = command.trim().split(' ')[0].toLowerCase();
    return basicCommands.includes(firstWord);
  }

  private async executeBasicCommand(command: string) {
    const parts = command.trim().split(' ');
    const cmd = parts[0].toLowerCase();
    const args = parts.slice(1);

    try {
      switch (cmd) {
        case 'goto':
          if (args.length === 0) {
            console.log("❌ Usage: goto <url>");
            break;
          }
          let url = args.join(' ');
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }
          console.log(`🌐 Navigating to: ${url}`);
          await this.page.goto(url);
          console.log("✅ Navigation completed");
          break;

        case 'click':
          if (args.length === 0) {
            console.log("❌ Usage: click <selector>");
            break;
          }
          const selector = args.join(' ');
          console.log(`🖱️ Clicking: ${selector}`);
          await this.page.click(selector);
          console.log("✅ Clicked");
          break;

        case 'type':
          if (args.length < 2) {
            console.log("❌ Usage: type <selector> <text>");
            break;
          }
          const typeSelector = args[0];
          const typeText = args.slice(1).join(' ');
          console.log(`⌨️ Typing "${typeText}" into: ${typeSelector}`);
          await this.page.type(typeSelector, typeText);
          console.log("✅ Text typed");
          break;

        case 'title':
          const title = await this.page.title();
          console.log(`📄 Page title: ${title}`);
          break;

        case 'url':
          console.log(`🔗 Current URL: ${this.page.url()}`);
          break;

        case 'screenshot':
          const filename = args[0] || `gemini-pro-screenshot-${Date.now()}.png`;
          console.log(`📸 Taking screenshot: ${filename}`);
          await this.page.screenshot({ path: filename });
          console.log(`✅ Screenshot saved: ${filename}`);
          break;

        case 'wait':
          const ms = parseInt(args[0]) || 1000;
          console.log(`⏳ Waiting ${ms}ms...`);
          await this.page.waitForTimeout(ms);
          console.log("✅ Wait completed");
          break;

        case 'help':
          await this.showHelp();
          break;

        case 'exit':
          console.log("👋 Closing browser and exiting...");
          await this.stagehand.close();
          this.rl.close();
          process.exit(0);
          break;

        default:
          console.log(`❌ Unknown command: ${cmd}`);
          console.log("💡 Type 'help' for available commands");
          break;
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  private async executeNaturalLanguageCommand(command: string) {
    if (!this.hasApiKey) {
      console.log("❌ Natural language commands require Gemini API key.");
      console.log("💡 Use basic commands like: goto google.com");
      return;
    }

    try {
      console.log(`🚀 Gemini 2.0 Flash processing: "${command}"`);

      const lowerCommand = command.toLowerCase();

      // 智能导航命令
      if (lowerCommand.includes('navigate') || lowerCommand.includes('go to') || lowerCommand.includes('visit')) {
        // 尝试智能解析网站
        const sitePatterns = [
          { pattern: /github|git hub/i, url: 'https://github.com' },
          { pattern: /google/i, url: 'https://google.com' },
          { pattern: /youtube/i, url: 'https://youtube.com' },
          { pattern: /stackoverflow|stack overflow/i, url: 'https://stackoverflow.com' },
          { pattern: /python.*doc|python.*documentation/i, url: 'https://docs.python.org' },
          { pattern: /mdn|mozilla.*doc/i, url: 'https://developer.mozilla.org' },
        ];

        for (const site of sitePatterns) {
          if (site.pattern.test(command)) {
            console.log(`🌐 Smart navigation to: ${site.url}`);
            await this.page.goto(site.url);
            console.log("✅ Navigation completed");
            return;
          }
        }

        // 如果没有匹配到预设网站，尝试提取 URL
        const urlMatch = command.match(/https?:\/\/[^\s]+/);
        if (urlMatch) {
          console.log(`🌐 Navigating to: ${urlMatch[0]}`);
          await this.page.goto(urlMatch[0]);
          console.log("✅ Navigation completed");
          return;
        }
      }

      // 信息提取命令
      if (lowerCommand.includes('extract') || lowerCommand.includes('get') || lowerCommand.includes('find all') || lowerCommand.includes('describe')) {
        console.log("📊 Extracting information with Gemini 2.0 Flash...");
        const result = await this.page.extract({
          instruction: command,
          schema: z.object({
            result: z.string(),
          }),
        });
        console.log(`📋 Gemini 2.0 Flash Result: ${result.result}`);
        return;
      }

      // 观察命令
      if (lowerCommand.includes('show') || lowerCommand.includes('list') || lowerCommand.includes('observe') || lowerCommand.includes('find')) {
        console.log("👁️ Observing page with Gemini 2.0 Flash...");
        const observations = await this.page.observe({
          instruction: command
        });
        console.log("🔍 Gemini 2.0 Flash found these elements:");
        observations.slice(0, 8).forEach((obs: any, i: number) => {
          console.log(`   ${i + 1}. ${obs.description}`);
        });
        return;
      }

      // 其他动作命令
      console.log("🎯 Executing action with Gemini 2.0 Flash...");
      await this.page.act(command);
      console.log("✅ Gemini 2.0 Flash action completed");

    } catch (error) {
      console.log(`❌ Gemini 2.0 Flash Error: ${error.message}`);
      
      if (error.message.includes('location') || error.message.includes('region')) {
        console.log("🌍 地区限制问题 - 请确保代理正在运行");
        console.log("💡 尝试: ./start-with-proxy.sh");
      } else if (error.message.includes('quota') || error.message.includes('limit')) {
        console.log("📊 API配额限制 - 请检查您的 Gemini API 使用情况");
      } else {
        console.log("💡 Try a more specific command or use basic syntax");
      }
    }
  }

  async start() {
    await this.init();
    
    if (!this.stagehand || !this.page) {
      console.log("❌ Failed to initialize browser. Exiting...");
      this.rl.close();
      return;
    }
    
    while (true) {
      const command = await this.question('🚀 gemini-2.0> ');
      
      if (command.trim() === '') continue;
      if (command.trim() === 'exit') {
        await this.executeBasicCommand('exit');
        break;
      }

      // 判断是基础命令还是自然语言命令
      if (this.isBasicCommand(command)) {
        await this.executeBasicCommand(command);
      } else {
        await this.executeNaturalLanguageCommand(command);
      }
    }
  }
}

// 启动 Gemini 2.0 Flash 控制
if (import.meta.url === `file://${process.argv[1]}`) {
  const controller = new GeminiProController();
  controller.start().catch(console.error);
}
