#!/usr/bin/env tsx

import { Stagehand } from '@browserbasehq/stagehand';
import { z } from 'zod';
import * as readline from 'readline';
import { config } from 'dotenv';

// 加载环境变量
config();

class Gemini25Controller {
  private stagehand: any;
  private page: any;
  private rl: readline.Interface;
  private hasApiKey: boolean;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.hasApiKey = !!(process.env.OPENAI_API_KEY && process.env.OPENAI_BASE_URL);
  }

  async init() {
    console.log("🚀 Stagehand + Gemini 2.5 Flash Preview Control");
    console.log("=================================================");
    
    if (!this.hasApiKey) {
      console.log("❌ OpenAI compatible API configuration not found!");
      console.log("💡 Please check your .env file:");
      console.log("   OPENAI_API_KEY=sk-bqxasFqOsyx0i0J9AeFcC151Bf174211832a4e0407C6AbEf");
      console.log("   OPENAI_BASE_URL=https://one.glbai.com/v1");
      return;
    }

    console.log("✅ Gemini 2.5 Flash Preview API Configuration:");
    console.log(`🔑 API Key: ${process.env.OPENAI_API_KEY?.substring(0, 20)}...`);
    console.log(`🌐 Base URL: ${process.env.OPENAI_BASE_URL}`);
    console.log("🤖 Model: gemini-2.5-flash-preview");
    console.log("🌟 Advanced natural language processing available!");

    try {
      this.stagehand = new Stagehand({
        env: "LOCAL",
        verbose: 1,
        enableCaching: false,
        modelName: "gpt-4o-mini", // 使用标准模型名，您的服务端会映射到 Gemini 2.5
        modelClientOptions: {
          apiKey: process.env.OPENAI_API_KEY,
          baseURL: process.env.OPENAI_BASE_URL,
        },
        localBrowserLaunchOptions: {
          headless: process.env.DEMO_HEADLESS === 'true',
          viewport: {
            width: parseInt(process.env.DEMO_VIEWPORT_WIDTH || '1280'),
            height: parseInt(process.env.DEMO_VIEWPORT_HEIGHT || '720'),
          },
        },
      });

      await this.stagehand.init();
      this.page = this.stagehand.page;
      
      console.log("✅ Browser initialized with Gemini 2.5 Flash Preview!");
      console.log("💬 Ready for advanced natural language commands...\n");
      
      console.log("🎯 Try these enhanced commands:");
      console.log("  'Navigate to the GitHub homepage and find the trending repositories'");
      console.log("  'Go to Google and search for artificial intelligence news'");
      console.log("  'Find and click the login button on this page'");
      console.log("  'Extract all the article titles and their links'");
      console.log("  'Describe what you see on this page in detail'");
      console.log("  'Find all interactive elements and list them'");
      console.log("");
      
    } catch (error) {
      console.error("❌ Initialization error:", error.message);
      console.log("\n💡 Troubleshooting:");
      console.log("1. Check if your API key is valid");
      console.log("2. Verify the base URL is accessible");
      console.log("3. Ensure you have sufficient API credits");
    }
  }

  private question(prompt: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  private async showHelp() {
    console.log("\n📖 Gemini 2.5 Flash Preview Commands:");
    console.log("======================================");
    
    if (this.hasApiKey) {
      console.log("🤖 Advanced Natural Language Commands:");
      console.log("  'Navigate to [detailed description]'");
      console.log("  'Search for [complex query]'");
      console.log("  'Find and interact with [element description]'");
      console.log("  'Extract [specific data requirements]'");
      console.log("  'Analyze and describe [what to analyze]'");
      console.log("  'Perform [complex multi-step task]'");
      console.log("");
      console.log("💡 Enhanced Examples:");
      console.log("  'Go to the Python official website and navigate to the documentation section'");
      console.log("  'Search for the latest machine learning research papers'");
      console.log("  'Find the contact form and fill it with sample data'");
      console.log("  'Extract all product information including names, prices, and descriptions'");
      console.log("  'Analyze the page layout and describe the main sections'");
      console.log("  'Find all social media links and list their platforms'");
      console.log("");
    }
    
    console.log("🔧 Basic Commands:");
    console.log("  goto <url>              - Navigate to URL");
    console.log("  click <selector>        - Click element");
    console.log("  type <selector> <text>  - Type text");
    console.log("  title                   - Get page title");
    console.log("  url                     - Get current URL");
    console.log("  screenshot [filename]   - Take screenshot");
    console.log("  config                  - Show API configuration");
    console.log("  help                    - Show this help");
    console.log("  exit                    - Close and exit");
    console.log("");
  }

  private isBasicCommand(command: string): boolean {
    const basicCommands = ['goto', 'click', 'type', 'fill', 'clear', 'title', 'url', 'screenshot', 'wait', 'config', 'help', 'exit'];
    const firstWord = command.trim().split(' ')[0].toLowerCase();
    return basicCommands.includes(firstWord);
  }

  private async executeBasicCommand(command: string) {
    const parts = command.trim().split(' ');
    const cmd = parts[0].toLowerCase();
    const args = parts.slice(1);

    try {
      switch (cmd) {
        case 'goto':
          if (args.length === 0) {
            console.log("❌ Usage: goto <url>");
            break;
          }
          let url = args.join(' ');
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }
          console.log(`🌐 Navigating to: ${url}`);
          await this.page.goto(url);
          console.log("✅ Navigation completed");
          break;

        case 'click':
          if (args.length === 0) {
            console.log("❌ Usage: click <selector>");
            break;
          }
          const selector = args.join(' ');
          console.log(`🖱️ Clicking: ${selector}`);
          await this.page.click(selector);
          console.log("✅ Clicked");
          break;

        case 'type':
          if (args.length < 2) {
            console.log("❌ Usage: type <selector> <text>");
            break;
          }
          const typeSelector = args[0];
          const typeText = args.slice(1).join(' ');
          console.log(`⌨️ Typing "${typeText}" into: ${typeSelector}`);
          await this.page.type(typeSelector, typeText);
          console.log("✅ Text typed");
          break;

        case 'title':
          const title = await this.page.title();
          console.log(`📄 Page title: ${title}`);
          break;

        case 'url':
          console.log(`🔗 Current URL: ${this.page.url()}`);
          break;

        case 'screenshot':
          const filename = args[0] || `gemini-25-screenshot-${Date.now()}.png`;
          console.log(`📸 Taking screenshot: ${filename}`);
          await this.page.screenshot({ path: filename });
          console.log(`✅ Screenshot saved: ${filename}`);
          break;

        case 'config':
          console.log("\n🔧 Current API Configuration:");
          console.log(`   Model: gemini-2.5-flash-preview-05-20`);
          console.log(`   Base URL: ${process.env.OPENAI_BASE_URL}`);
          console.log(`   API Key: ${process.env.OPENAI_API_KEY?.substring(0, 20)}...`);
          break;

        case 'help':
          await this.showHelp();
          break;

        case 'exit':
          console.log("👋 Closing browser and exiting...");
          await this.stagehand.close();
          this.rl.close();
          process.exit(0);
          break;

        default:
          console.log(`❌ Unknown command: ${cmd}`);
          console.log("💡 Type 'help' for available commands");
          break;
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  private async executeNaturalLanguageCommand(command: string) {
    if (!this.hasApiKey) {
      console.log("❌ Natural language commands require API configuration.");
      return;
    }

    try {
      console.log(`🚀 Gemini 2.5 Flash Preview processing: "${command}"`);

      const lowerCommand = command.toLowerCase();

      // 智能导航 - 支持更复杂的描述
      if (lowerCommand.includes('navigate') || lowerCommand.includes('go to') || lowerCommand.includes('visit')) {
        const sitePatterns = [
          { pattern: /github/i, url: 'https://github.com' },
          { pattern: /google/i, url: 'https://google.com' },
          { pattern: /youtube/i, url: 'https://youtube.com' },
          { pattern: /python.*doc|python.*official/i, url: 'https://docs.python.org' },
          { pattern: /stackoverflow/i, url: 'https://stackoverflow.com' },
          { pattern: /mdn|mozilla/i, url: 'https://developer.mozilla.org' },
          { pattern: /wikipedia/i, url: 'https://wikipedia.org' },
          { pattern: /reddit/i, url: 'https://reddit.com' },
        ];

        for (const site of sitePatterns) {
          if (site.pattern.test(command)) {
            console.log(`🌐 Smart navigation to: ${site.url}`);
            await this.page.goto(site.url);
            console.log("✅ Navigation completed");
            return;
          }
        }

        // 如果没有匹配预设网站，使用 AI 执行导航
        console.log("🎯 Using AI for complex navigation...");
        await this.page.act(command);
        console.log("✅ AI navigation completed");
        return;
      }

      // 信息提取 - 支持复杂的数据提取
      if (lowerCommand.includes('extract') || lowerCommand.includes('get') || lowerCommand.includes('analyze') || lowerCommand.includes('describe')) {
        console.log("📊 Advanced information extraction...");
        const result = await this.page.extract({
          instruction: command,
          schema: z.object({
            result: z.string(),
          }),
        });
        console.log(`📋 Gemini 2.5 Flash Result: ${result.result}`);
        return;
      }

      // 页面观察 - 支持详细的元素发现
      if (lowerCommand.includes('find') || lowerCommand.includes('show') || lowerCommand.includes('list') || lowerCommand.includes('discover')) {
        console.log("👁️ Advanced page observation...");
        const observations = await this.page.observe({
          instruction: command
        });
        console.log("🔍 Gemini 2.5 Flash found these elements:");
        observations.slice(0, 10).forEach((obs: any, i: number) => {
          console.log(`   ${i + 1}. ${obs.description}`);
        });
        return;
      }

      // 执行复杂动作
      console.log("🎯 Executing advanced action...");
      await this.page.act(command);
      console.log("✅ Gemini 2.5 Flash action completed");

    } catch (error) {
      console.log(`❌ Gemini 2.5 Flash Error: ${error.message}`);
      if (error.message.includes('quota') || error.message.includes('limit')) {
        console.log("📊 API quota limit reached - please check your account");
      } else if (error.message.includes('unauthorized')) {
        console.log("🔑 API key authentication failed - please check your credentials");
      } else {
        console.log("💡 Try a more specific command or use basic syntax");
      }
    }
  }

  async start() {
    await this.init();
    
    if (!this.stagehand || !this.page) {
      console.log("❌ Failed to initialize browser. Exiting...");
      this.rl.close();
      return;
    }
    
    while (true) {
      const command = await this.question('🚀 gemini-2.5> ');
      
      if (command.trim() === '') continue;
      if (command.trim() === 'exit') {
        await this.executeBasicCommand('exit');
        break;
      }

      if (this.isBasicCommand(command)) {
        await this.executeBasicCommand(command);
      } else {
        await this.executeNaturalLanguageCommand(command);
      }
    }
  }
}

// 启动 Gemini 2.5 Flash Preview 控制
if (import.meta.url === `file://${process.argv[1]}`) {
  const controller = new Gemini25Controller();
  controller.start().catch(console.error);
}
