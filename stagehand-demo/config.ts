import { config } from 'dotenv';
import { Stagehand } from '@browserbasehq/stagehand';

// 加载环境变量
config();

export interface DemoConfig {
  headless: boolean;
  viewport: {
    width: number;
    height: number;
  };
  verbose: number;
  modelName?: string;
  apiKey?: string;
}

export const defaultConfig: DemoConfig = {
  headless: process.env.DEMO_HEADLESS === 'true',
  viewport: {
    width: parseInt(process.env.DEMO_VIEWPORT_WIDTH || '1280'),
    height: parseInt(process.env.DEMO_VIEWPORT_HEIGHT || '720'),
  },
  verbose: parseInt(process.env.DEMO_VERBOSE || '1'),
  modelName: getModelName(),
  apiKey: getApiKey(),
};

function getModelName(): string {
  if (process.env.GOOGLE_API_KEY) {
    return 'google/gemini-2.0-flash-exp'; // 使用最新的 Gemini 2.0 Flash
  }
  if (process.env.OPENAI_API_KEY) {
    return 'gpt-4o-mini';
  }
  return 'gpt-4o-mini'; // 默认
}

function getApiKey(): string | undefined {
  return process.env.GOOGLE_API_KEY || process.env.OPENAI_API_KEY;
}

export function createStagehand(config: DemoConfig = defaultConfig): Stagehand {
  const stagehandConfig: any = {
    env: "LOCAL",
    verbose: config.verbose,
    enableCaching: false,
    localBrowserLaunchOptions: {
      headless: config.headless,
      viewport: config.viewport,
    },
  };

  // 如果有 API key，添加模型配置
  if (config.apiKey) {
    stagehandConfig.modelName = config.modelName;
    stagehandConfig.modelClientOptions = {
      apiKey: config.apiKey,
    };
  }

  return new Stagehand(stagehandConfig);
}

export function checkApiKey(): boolean {
  return !!(process.env.GOOGLE_API_KEY || process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY);
}

export function getApiKeyInstructions(): string {
  return `
🔑 API Key Setup Instructions:

1. Copy the environment file:
   cp .env.example .env

2. Edit .env and add your API key:
   - For Google Gemini: Get key from https://aistudio.google.com/app/apikey
   - For OpenAI: Get key from https://platform.openai.com/api-keys
   - For Anthropic: Get key from https://console.anthropic.com/

3. Example .env content:
   GOOGLE_API_KEY=your-gemini-key-here
   # or
   OPENAI_API_KEY=sk-your-openai-key-here

4. Run the demo again:
   npm run ai
`;
}
