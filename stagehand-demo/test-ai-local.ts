#!/usr/bin/env tsx

import { Stagehand } from '@browserbasehq/stagehand';
import { z } from 'zod';
import { config } from 'dotenv';
import path from 'path';

// 加载环境变量
config();

async function testAiLocal() {
  console.log("🧪 Testing Gemini 2.5 Flash Preview AI with Local File");
  console.log("=======================================================");
  
  console.log("📋 Configuration:");
  console.log(`🔑 API Key: ${process.env.OPENAI_API_KEY?.substring(0, 20)}...`);
  console.log(`🌐 Base URL: ${process.env.OPENAI_BASE_URL}`);
  console.log("");

  let stagehand;
  try {
    // 使用标准 OpenAI 兼容配置
    stagehand = new Stagehand({
      env: "LOCAL",
      verbose: 1,
      enableCaching: false,
      modelName: "gpt-4o-mini", // 使用标准模型名，让服务端处理
      modelClientOptions: {
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL,
      },
      localBrowserLaunchOptions: {
        headless: false, // 显示浏览器
        viewport: { width: 1280, height: 720 },
      },
    });

    await stagehand.init();
    console.log("✅ Stagehand initialization: SUCCESS");

    const page = stagehand.page;
    
    // 使用本地测试文件
    const testHtmlPath = path.join(process.cwd(), "test.html");
    const fileUrl = `file://${testHtmlPath}`;
    
    console.log(`🌐 Loading local test file: ${fileUrl}`);
    await page.goto(fileUrl);
    
    const title = await page.title();
    console.log(`✅ Navigation: SUCCESS - Title: ${title}`);

    // 测试 AI 提取功能
    console.log("\n🤖 Testing AI extract function...");
    try {
      const result = await page.extract({
        instruction: "Extract the main heading and description from this test page",
        schema: z.object({
          heading: z.string(),
          description: z.string(),
        }),
      });
      console.log("✅ AI Extract: SUCCESS");
      console.log(`   📄 Heading: ${result.heading}`);
      console.log(`   📝 Description: ${result.description}`);
    } catch (aiError) {
      console.log(`❌ AI Extract: FAILED - ${aiError.message}`);
    }

    // 测试 AI 观察功能
    console.log("\n👁️ Testing AI observe function...");
    try {
      const observations = await page.observe({
        instruction: "Find all interactive elements like buttons and input fields"
      });
      console.log(`✅ AI Observe: SUCCESS - Found ${observations.length} elements`);
      observations.slice(0, 5).forEach((obs, i) => {
        console.log(`   ${i + 1}. ${obs.description}`);
      });
    } catch (aiError) {
      console.log(`❌ AI Observe: FAILED - ${aiError.message}`);
    }

    // 测试 AI 动作功能
    console.log("\n🎯 Testing AI act function...");
    try {
      await page.act("Type 'AI Test' in the search input field");
      console.log("✅ AI Act (Type): SUCCESS");
      
      // 验证输入
      const inputValue = await page.inputValue('#searchInput');
      console.log(`   📝 Input value: "${inputValue}"`);
      
      await page.act("Click the search button");
      console.log("✅ AI Act (Click): SUCCESS");
      
      // 等待结果
      await page.waitForTimeout(1000);
      
      // 检查结果
      const resultVisible = await page.isVisible('#result');
      console.log(`   📊 Result visible: ${resultVisible}`);
      
      if (resultVisible) {
        const resultText = await page.textContent('#resultText');
        console.log(`   📋 Result text: "${resultText}"`);
      }
      
    } catch (aiError) {
      console.log(`❌ AI Act: FAILED - ${aiError.message}`);
    }

    // 测试复杂的 AI 提取
    console.log("\n📊 Testing complex AI extraction...");
    try {
      const complexResult = await page.extract({
        instruction: "Extract all button texts, the current search input value, and whether the result is visible",
        schema: z.object({
          buttons: z.array(z.string()),
          searchValue: z.string(),
          resultVisible: z.boolean(),
        }),
      });
      console.log("✅ Complex AI Extract: SUCCESS");
      console.log(`   🔘 Buttons: ${complexResult.buttons.join(', ')}`);
      console.log(`   🔍 Search Value: "${complexResult.searchValue}"`);
      console.log(`   👁️ Result Visible: ${complexResult.resultVisible}`);
    } catch (aiError) {
      console.log(`❌ Complex AI Extract: FAILED - ${aiError.message}`);
    }

    await stagehand.close();
    console.log("\n✅ All tests completed successfully!");
    
    console.log("\n🎉 Your Gemini 2.5 Flash Preview API is working!");
    console.log("🚀 You can now use the full control script:");
    console.log("   npm run gemini-25");

  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
    if (stagehand) {
      try {
        await stagehand.close();
      } catch (closeError) {
        // Ignore cleanup errors
      }
    }
    
    console.log("\n💡 Troubleshooting suggestions:");
    console.log("1. Verify your API key is correct");
    console.log("2. Check if the base URL is accessible");
    console.log("3. Ensure your API service supports the model name");
    console.log("4. Try using a different model name like 'gpt-4' or 'gpt-3.5-turbo'");
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testAiLocal().catch(console.error);
}
