#!/usr/bin/env tsx

import { createStagehand } from './config.js';
import path from 'path';

async function enhancedDemo() {
  console.log("🚀 Stagehand Enhanced Demo");
  console.log("==========================");
  console.log("Comprehensive browser automation showcase (no API key required)\n");

  const stagehand = createStagehand();

  try {
    await stagehand.init();
    console.log("✅ Stagehand initialized successfully!");

    const page = stagehand.page;
    const testHtmlPath = path.join(process.cwd(), "test.html");
    const fileUrl = `file://${testHtmlPath}`;

    console.log("\n📍 Step 1: Advanced Page Navigation");
    console.log("-----------------------------------");
    await page.goto(fileUrl);
    
    const title = await page.title();
    const url = page.url();
    console.log(`📄 Page title: ${title}`);
    console.log(`🔗 URL: ${url}`);

    console.log("\n🔍 Step 2: Element Discovery & Analysis");
    console.log("--------------------------------------");
    
    // 查找所有按钮
    const buttons = await page.$$('button');
    console.log(`🔘 Found ${buttons.length} buttons on the page:`);
    
    for (let i = 0; i < buttons.length; i++) {
      const text = await buttons[i].textContent();
      const isVisible = await buttons[i].isVisible();
      const isEnabled = await buttons[i].isEnabled();
      console.log(`   ${i + 1}. "${text}" (visible: ${isVisible}, enabled: ${isEnabled})`);
    }

    // 查找所有输入框
    const inputs = await page.$$('input');
    console.log(`📝 Found ${inputs.length} input field(s):`);
    
    for (let i = 0; i < inputs.length; i++) {
      const type = await inputs[i].getAttribute('type');
      const placeholder = await inputs[i].getAttribute('placeholder');
      const id = await inputs[i].getAttribute('id');
      console.log(`   ${i + 1}. Type: ${type}, ID: ${id}, Placeholder: "${placeholder}"`);
    }

    console.log("\n🎯 Step 3: Interactive Form Testing");
    console.log("-----------------------------------");
    
    const testData = [
      'Hello Stagehand!',
      'Browser Automation',
      'AI Web Testing',
      'Playwright Integration'
    ];

    for (let i = 0; i < testData.length; i++) {
      console.log(`🔄 Test ${i + 1}/${testData.length}: "${testData[i]}"`);
      
      // 清空输入框
      await page.fill('#searchInput', '');
      
      // 逐字符输入（模拟真实用户）
      await page.type('#searchInput', testData[i], { delay: 50 });
      
      // 获取当前值
      const currentValue = await page.inputValue('#searchInput');
      console.log(`   ✏️  Input value: "${currentValue}"`);
      
      // 点击搜索
      await page.click('button:has-text("Search")');
      await page.waitForTimeout(500);
      
      // 检查结果
      const resultVisible = await page.isVisible('#result');
      if (resultVisible) {
        const resultText = await page.textContent('#resultText');
        console.log(`   📊 Result: "${resultText}"`);
      }
      
      // 清除结果
      await page.click('button:has-text("Clear")');
      await page.waitForTimeout(300);
      
      console.log(`   ✅ Test ${i + 1} completed\n`);
    }

    console.log("🎨 Step 4: CSS & Style Analysis");
    console.log("-------------------------------");
    
    // 获取页面样式信息
    const backgroundColor = await page.evaluate(() => {
      return window.getComputedStyle(document.body).backgroundColor;
    });
    
    const containerStyle = await page.evaluate(() => {
      const container = document.querySelector('.container');
      if (container) {
        const style = window.getComputedStyle(container);
        return {
          backgroundColor: style.backgroundColor,
          padding: style.padding,
          borderRadius: style.borderRadius,
        };
      }
      return null;
    });

    console.log(`🎨 Body background: ${backgroundColor}`);
    if (containerStyle) {
      console.log(`📦 Container styles:`, containerStyle);
    }

    console.log("\n📱 Step 5: Responsive Testing");
    console.log("-----------------------------");
    
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop Large' },
      { width: 1280, height: 720, name: 'Desktop Standard' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' },
    ];

    for (const viewport of viewports) {
      console.log(`📱 Testing ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize(viewport);
      await page.waitForTimeout(500);
      
      // 检查元素是否仍然可见
      const searchInputVisible = await page.isVisible('#searchInput');
      const buttonsVisible = await page.isVisible('button:has-text("Search")');
      
      console.log(`   📝 Search input visible: ${searchInputVisible}`);
      console.log(`   🔘 Buttons visible: ${buttonsVisible}`);
    }

    // 恢复默认视口
    await page.setViewportSize({ width: 1280, height: 720 });

    console.log("\n🔔 Step 6: Event Handling");
    console.log("-------------------------");
    
    let dialogCount = 0;
    page.on('dialog', async dialog => {
      dialogCount++;
      console.log(`🔔 Dialog ${dialogCount}: "${dialog.message()}"`);
      console.log(`   Type: ${dialog.type()}`);
      await dialog.accept();
    });

    // 触发对话框
    await page.click('button:has-text("Show Info")');
    await page.waitForTimeout(1000);

    console.log("\n📊 Step 7: Performance Metrics");
    console.log("------------------------------");
    
    // 测量页面加载性能
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return {
        domContentLoaded: Math.round(navigation.domContentLoadedEventEnd - navigation.navigationStart),
        loadComplete: Math.round(navigation.loadEventEnd - navigation.navigationStart),
        domElements: document.querySelectorAll('*').length,
      };
    });

    console.log(`⚡ DOM Content Loaded: ${performanceMetrics.domContentLoaded}ms`);
    console.log(`🏁 Load Complete: ${performanceMetrics.loadComplete}ms`);
    console.log(`🧱 Total DOM Elements: ${performanceMetrics.domElements}`);

    console.log("\n🎉 Enhanced Demo Summary");
    console.log("========================");
    console.log("✅ Page navigation and analysis");
    console.log("✅ Element discovery and interaction");
    console.log("✅ Form automation with multiple test cases");
    console.log("✅ CSS and style analysis");
    console.log("✅ Responsive design testing");
    console.log("✅ Event handling and dialogs");
    console.log("✅ Performance metrics collection");
    console.log(`✅ Total dialogs handled: ${dialogCount}`);

    console.log("\n💡 Next Steps:");
    console.log("- Set up an OpenAI API key to try AI features");
    console.log("- Modify test.html to test different scenarios");
    console.log("- Explore real website automation with npm run local");

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await stagehand.close();
    console.log("\n✅ Enhanced demo finished!");
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  enhancedDemo().catch(console.error);
}
