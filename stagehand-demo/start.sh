#!/bin/bash

# Stagehand Demo Quick Start Script
# =================================

echo "🎭 Stagehand Demo Quick Start"
echo "============================="
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the stagehand-demo directory"
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check for Node.js
if ! command_exists node; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

echo "✅ Node.js found: $(node --version)"

# Check for npm
if ! command_exists npm; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm found: $(npm --version)"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo ""
    echo "📦 Installing dependencies..."
    npm install
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
    
    echo "✅ Dependencies installed successfully!"
fi

# Check if browsers are installed
echo ""
echo "🌐 Checking Playwright browsers..."
if ! npm run install-browsers > /dev/null 2>&1; then
    echo "⚠️  Installing Playwright browsers..."
    npm run install-browsers
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install browsers"
        exit 1
    fi
fi

echo "✅ Browsers ready!"

# Show menu
echo ""
echo "🚀 What would you like to do?"
echo ""
echo "1) Run Basic Demo (no API key needed)"
echo "2) Run AI Demo (requires API key)"
echo "3) Run Examples Collection"
echo "4) Run Test Suite"
echo "5) Setup API Key"
echo "6) Show Help"
echo "7) Exit"
echo ""

read -p "Choose an option (1-7): " choice

case $choice in
    1)
        echo ""
        echo "🎭 Running Basic Demo..."
        npm run basic
        ;;
    2)
        echo ""
        if [ ! -f ".env" ]; then
            echo "⚠️  No .env file found. You need to set up your API key first."
            echo "Would you like to set it up now? (y/n)"
            read -p "> " setup_key
            if [ "$setup_key" = "y" ] || [ "$setup_key" = "Y" ]; then
                cp .env.example .env
                echo "📝 Created .env file. Please edit it with your API key:"
                echo "   OPENAI_API_KEY=your-key-here"
                echo ""
                echo "Then run: npm run ai"
            fi
        else
            echo "🤖 Running AI Demo..."
            npm run ai
        fi
        ;;
    3)
        echo ""
        echo "📚 Running Examples Collection..."
        npm run examples
        ;;
    4)
        echo ""
        echo "🧪 Running Test Suite..."
        npm test
        ;;
    5)
        echo ""
        echo "🔑 Setting up API Key..."
        if [ ! -f ".env" ]; then
            cp .env.example .env
            echo "✅ Created .env file from template"
        fi
        echo ""
        echo "📝 Please edit the .env file and add your API key:"
        echo "   OPENAI_API_KEY=your-key-here"
        echo ""
        echo "Get your API key from:"
        echo "   OpenAI: https://platform.openai.com/api-keys"
        echo "   Anthropic: https://console.anthropic.com/"
        ;;
    6)
        echo ""
        echo "📖 Stagehand Demo Help"
        echo "====================="
        echo ""
        echo "Available commands:"
        echo "  npm run basic     - Basic demo (no API key)"
        echo "  npm run ai        - AI demo (requires API key)"
        echo "  npm run examples  - Multiple examples"
        echo "  npm test          - Test suite"
        echo "  npm run local     - Test with real websites"
        echo ""
        echo "Setup:"
        echo "  cp .env.example .env"
        echo "  # Edit .env with your API key"
        echo ""
        echo "Documentation:"
        echo "  cat README.md"
        ;;
    7)
        echo ""
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo ""
        echo "❌ Invalid option. Please choose 1-7."
        exit 1
        ;;
esac

echo ""
echo "✅ Done! Run ./start.sh again to try other demos."
