#!/usr/bin/env tsx

import { createStagehand, checkApi<PERSON><PERSON> } from './config.js';
import { z } from 'zod';
import path from 'path';

async function runExamples() {
  console.log("📚 Stagehand Examples Collection");
  console.log("================================");
  console.log("Multiple examples demonstrating different Stagehand features.\n");

  const hasApiKey = checkApiKey();
  const stagehand = createStagehand();

  try {
    await stagehand.init();
    console.log("✅ Stagehand initialized!");

    const page = stagehand.page;
    const testHtmlPath = path.join(process.cwd(), "test.html");
    const fileUrl = `file://${testHtmlPath}`;

    await page.goto(fileUrl);

    // Example 1: Form Automation
    console.log("\n📝 Example 1: Form Automation");
    console.log("------------------------------");
    
    if (hasApi<PERSON><PERSON>) {
      await page.act("Type 'Form automation example' in the search input");
      await page.act("Click the search button");
      
      const formResult = await page.extract({
        instruction: "Get the search result text",
        schema: z.object({ text: z.string() }),
      });
      console.log(`✅ Form result: ${formResult.text}`);
    } else {
      await page.fill('#searchInput', 'Form automation example');
      await page.click('button:has-text("Search")');
      await page.waitForTimeout(1000);
      
      const resultText = await page.textContent('#resultText');
      console.log(`✅ Form result: ${resultText}`);
    }

    // Example 2: Element Discovery
    console.log("\n🔍 Example 2: Element Discovery");
    console.log("-------------------------------");
    
    if (hasApiKey) {
      const elements = await page.observe({
        instruction: "Find all buttons on the page"
      });
      
      console.log("🤖 AI found these buttons:");
      elements.forEach((el, i) => {
        console.log(`${i + 1}. ${el.description}`);
      });
    } else {
      const buttons = await page.$$('button');
      console.log(`📊 Found ${buttons.length} buttons using basic selector`);
      
      for (let i = 0; i < buttons.length; i++) {
        const text = await buttons[i].textContent();
        console.log(`${i + 1}. Button: "${text}"`);
      }
    }

    // Example 3: Data Extraction
    console.log("\n📊 Example 3: Data Extraction");
    console.log("-----------------------------");
    
    await page.click('button:has-text("Clear")');
    await page.waitForTimeout(500);
    
    if (hasApiKey) {
      const pageData = await page.extract({
        instruction: "Extract all the text from the info box and the page title",
        schema: z.object({
          title: z.string(),
          infoText: z.string(),
          availableActions: z.array(z.string()),
        }),
      });
      
      console.log("🤖 AI extracted data:");
      console.log(`📄 Title: ${pageData.title}`);
      console.log(`ℹ️  Info: ${pageData.infoText.substring(0, 50)}...`);
      console.log(`🎯 Actions: ${pageData.availableActions.join(', ')}`);
    } else {
      const title = await page.title();
      const infoText = await page.textContent('.info-box');
      const actionsList = await page.textContent('ul');
      
      console.log("📊 Basic extraction:");
      console.log(`📄 Title: ${title}`);
      console.log(`ℹ️  Info: ${infoText?.substring(0, 50)}...`);
      console.log(`🎯 Actions: ${actionsList?.substring(0, 50)}...`);
    }

    // Example 4: Sequential Actions
    console.log("\n🔄 Example 4: Sequential Actions");
    console.log("--------------------------------");
    
    const testInputs = ['Test 1', 'Test 2', 'Test 3'];
    
    for (let i = 0; i < testInputs.length; i++) {
      console.log(`🔄 Running test ${i + 1}/${testInputs.length}`);
      
      if (hasApiKey) {
        await page.act(`Type '${testInputs[i]}' in the search box`);
        await page.act("Click the search button");
        await page.waitForTimeout(500);
        await page.act("Click the clear button");
      } else {
        await page.fill('#searchInput', testInputs[i]);
        await page.click('button:has-text("Search")');
        await page.waitForTimeout(500);
        await page.click('button:has-text("Clear")');
      }
      
      console.log(`✅ Completed test ${i + 1}`);
    }

    console.log("\n🎉 All examples completed successfully!");
    
    if (!hasApiKey) {
      console.log("\n💡 Pro tip: Set up an API key to see AI-powered features!");
      console.log("   cp .env.example .env");
      console.log("   # Edit .env with your API key");
      console.log("   npm run ai");
    }

  } catch (error) {
    console.error("❌ Error in examples:", error);
  } finally {
    await stagehand.close();
    console.log("✅ Examples finished!");
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  runExamples().catch(console.error);
}
