#!/usr/bin/env tsx

import { createStagehand, checkApi<PERSON>ey } from './config.js';
import { z } from 'zod';

async function localDemo() {
  console.log("🌐 Stagehand Local Website Demo");
  console.log("===============================");
  console.log("This demo tests with real websites (requires internet).\n");

  const hasApiKey = checkApiKey();
  const stagehand = createStagehand();

  try {
    await stagehand.init();
    console.log("✅ Stagehand initialized!");

    const page = stagehand.page;

    console.log("\n📍 Step 1: Testing with a simple website...");
    
    try {
      // 尝试访问一个简单的网站
      await page.goto("https://httpbin.org/html", { timeout: 10000 });
      console.log("✅ Successfully loaded httpbin.org");
      
      const title = await page.title();
      console.log(`📄 Page title: ${title}`);

      // 如果有 API key，使用 AI 功能
      if (hasApi<PERSON>ey) {
        console.log("\n🤖 Using AI to analyze the page...");
        
        const pageAnalysis = await page.extract({
          instruction: "Extract the main heading and any visible text content",
          schema: z.object({
            heading: z.string(),
            content: z.string(),
          }),
        });

        console.log("🤖 AI analysis:");
        console.log(`📄 Heading: ${pageAnalysis.heading}`);
        console.log(`📝 Content: ${pageAnalysis.content.substring(0, 100)}...`);
      } else {
        console.log("ℹ️  Basic mode (no AI) - getting page content...");
        const bodyText = await page.textContent('body');
        console.log(`📝 Page content preview: ${bodyText?.substring(0, 100)}...`);
      }

    } catch (error) {
      console.log("⚠️  Network test failed, trying local file...");
      
      // 回退到本地文件
      const path = await import('path');
      const testHtmlPath = path.join(process.cwd(), "test.html");
      const fileUrl = `file://${testHtmlPath}`;
      
      await page.goto(fileUrl);
      console.log("✅ Loaded local test file instead");
      
      if (hasApiKey) {
        await page.act("Type 'Network fallback test' in the search box");
        await page.act("Click the search button");
        
        const result = await page.extract({
          instruction: "Get the search result",
          schema: z.object({ result: z.string() }),
        });
        
        console.log(`🤖 AI result: ${result.result}`);
      } else {
        await page.fill('#searchInput', 'Network fallback test');
        await page.click('button:has-text("Search")');
        await page.waitForTimeout(1000);
        
        const resultText = await page.textContent('#resultText');
        console.log(`📝 Result: ${resultText}`);
      }
    }

    console.log("\n🎉 Local demo completed!");
    
    if (!hasApiKey) {
      console.log("💡 To enable AI features, set up your API key and run: npm run ai");
    }

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await stagehand.close();
    console.log("✅ Demo finished!");
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  localDemo().catch(console.error);
}
