#!/usr/bin/env tsx

import { Stagehand } from '@browserbasehq/stagehand';
import { z } from 'zod';
import * as readline from 'readline';
import { config } from 'dotenv';

// 加载环境变量
config();

class OpenAICompatibleController {
  private stagehand: any;
  private page: any;
  private rl: readline.Interface;
  private hasApiKey: boolean;
  private apiConfig: any;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.hasApiKey = !!(process.env.OPENAI_API_KEY || process.env.GOOGLE_API_KEY);
    this.apiConfig = this.getApiConfig();
  }

  private getApiConfig() {
    // 优先使用 OpenAI 兼容配置
    if (process.env.OPENAI_API_KEY && process.env.OPENAI_BASE_URL) {
      return {
        type: 'openai-compatible',
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: process.env.OPENAI_BASE_URL,
        model: 'gemini-2.0-flash-exp', // 或其他兼容模型
      };
    }
    
    // 使用标准 OpenAI
    if (process.env.OPENAI_API_KEY && !process.env.OPENAI_BASE_URL) {
      return {
        type: 'openai',
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: 'https://api.openai.com/v1/',
        model: 'gpt-4o-mini',
      };
    }

    // 使用 Google Gemini
    if (process.env.GOOGLE_API_KEY) {
      return {
        type: 'google',
        apiKey: process.env.GOOGLE_API_KEY,
        model: 'google/gemini-2.0-flash-exp',
      };
    }

    return null;
  }

  async init() {
    console.log("🔗 Stagehand OpenAI Compatible Control");
    console.log("======================================");
    
    if (!this.hasApiKey) {
      console.log("❌ No API key found!");
      console.log("💡 Please configure one of the following in .env:");
      console.log("   1. OpenAI Compatible: OPENAI_API_KEY + OPENAI_BASE_URL");
      console.log("   2. Standard OpenAI: OPENAI_API_KEY");
      console.log("   3. Google Gemini: GOOGLE_API_KEY");
      return;
    }

    console.log(`🔑 API Configuration: ${this.apiConfig.type}`);
    console.log(`🤖 Model: ${this.apiConfig.model}`);
    if (this.apiConfig.baseURL) {
      console.log(`🌐 Base URL: ${this.apiConfig.baseURL}`);
    }
    console.log("✅ Natural language features available!");

    try {
      // 创建 Stagehand 配置
      const stagehandConfig: any = {
        env: "LOCAL",
        verbose: 1,
        enableCaching: false,
        localBrowserLaunchOptions: {
          headless: process.env.DEMO_HEADLESS === 'true',
          viewport: {
            width: parseInt(process.env.DEMO_VIEWPORT_WIDTH || '1280'),
            height: parseInt(process.env.DEMO_VIEWPORT_HEIGHT || '720'),
          },
        },
      };

      // 根据 API 类型配置模型
      if (this.apiConfig.type === 'openai-compatible') {
        stagehandConfig.modelName = this.apiConfig.model;
        stagehandConfig.modelClientOptions = {
          apiKey: this.apiConfig.apiKey,
          baseURL: this.apiConfig.baseURL,
        };
      } else if (this.apiConfig.type === 'openai') {
        stagehandConfig.modelName = this.apiConfig.model;
        stagehandConfig.modelClientOptions = {
          apiKey: this.apiConfig.apiKey,
        };
      } else if (this.apiConfig.type === 'google') {
        stagehandConfig.modelName = this.apiConfig.model;
        stagehandConfig.modelClientOptions = {
          apiKey: this.apiConfig.apiKey,
        };
      }

      this.stagehand = new Stagehand(stagehandConfig);
      await this.stagehand.init();
      this.page = this.stagehand.page;
      
      console.log("✅ Browser initialized successfully!");
      console.log("💬 Ready for natural language commands...\n");
      
      console.log("🎯 Try these commands:");
      console.log("  'Go to Google'");
      console.log("  'Navigate to GitHub'");
      console.log("  'Search for machine learning'");
      console.log("  'Click the search button'");
      console.log("  'Extract page information'");
      console.log("");
      
    } catch (error) {
      console.error("❌ Initialization error:", error.message);
      if (error.message.includes('location')) {
        console.log("\n🌍 API 访问问题解决方案:");
        console.log("1. 检查代理设置");
        console.log("2. 尝试使用 OpenAI 兼容接口");
        console.log("3. 或使用标准 OpenAI API");
      }
    }
  }

  private question(prompt: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  private async showHelp() {
    console.log("\n📖 Available Commands:");
    console.log("======================");
    
    if (this.hasApiKey) {
      console.log("🤖 Natural Language Commands:");
      console.log("  'Go to [website]'");
      console.log("  'Navigate to [description]'");
      console.log("  'Search for [query]'");
      console.log("  'Click [element description]'");
      console.log("  'Type [text] in [field]'");
      console.log("  'Extract [information]'");
      console.log("  'Find all [elements]'");
      console.log("  'Describe this page'");
      console.log("");
      console.log("💡 Examples:");
      console.log("  'Go to the Python documentation'");
      console.log("  'Search for artificial intelligence'");
      console.log("  'Click the login button'");
      console.log("  'Extract all the links'");
      console.log("");
    }
    
    console.log("🔧 Basic Commands:");
    console.log("  goto <url>              - Navigate to URL");
    console.log("  click <selector>        - Click element");
    console.log("  type <selector> <text>  - Type text");
    console.log("  title                   - Get page title");
    console.log("  url                     - Get current URL");
    console.log("  screenshot [filename]   - Take screenshot");
    console.log("  config                  - Show API configuration");
    console.log("  help                    - Show this help");
    console.log("  exit                    - Close and exit");
    console.log("");
  }

  private isBasicCommand(command: string): boolean {
    const basicCommands = ['goto', 'click', 'type', 'fill', 'clear', 'title', 'url', 'screenshot', 'wait', 'config', 'help', 'exit'];
    const firstWord = command.trim().split(' ')[0].toLowerCase();
    return basicCommands.includes(firstWord);
  }

  private async executeBasicCommand(command: string) {
    const parts = command.trim().split(' ');
    const cmd = parts[0].toLowerCase();
    const args = parts.slice(1);

    try {
      switch (cmd) {
        case 'goto':
          if (args.length === 0) {
            console.log("❌ Usage: goto <url>");
            break;
          }
          let url = args.join(' ');
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }
          console.log(`🌐 Navigating to: ${url}`);
          await this.page.goto(url);
          console.log("✅ Navigation completed");
          break;

        case 'click':
          if (args.length === 0) {
            console.log("❌ Usage: click <selector>");
            break;
          }
          const selector = args.join(' ');
          console.log(`🖱️ Clicking: ${selector}`);
          await this.page.click(selector);
          console.log("✅ Clicked");
          break;

        case 'type':
          if (args.length < 2) {
            console.log("❌ Usage: type <selector> <text>");
            break;
          }
          const typeSelector = args[0];
          const typeText = args.slice(1).join(' ');
          console.log(`⌨️ Typing "${typeText}" into: ${typeSelector}`);
          await this.page.type(typeSelector, typeText);
          console.log("✅ Text typed");
          break;

        case 'title':
          const title = await this.page.title();
          console.log(`📄 Page title: ${title}`);
          break;

        case 'url':
          console.log(`🔗 Current URL: ${this.page.url()}`);
          break;

        case 'screenshot':
          const filename = args[0] || `openai-compatible-screenshot-${Date.now()}.png`;
          console.log(`📸 Taking screenshot: ${filename}`);
          await this.page.screenshot({ path: filename });
          console.log(`✅ Screenshot saved: ${filename}`);
          break;

        case 'config':
          console.log("\n🔧 Current API Configuration:");
          console.log(`   Type: ${this.apiConfig.type}`);
          console.log(`   Model: ${this.apiConfig.model}`);
          if (this.apiConfig.baseURL) {
            console.log(`   Base URL: ${this.apiConfig.baseURL}`);
          }
          console.log(`   API Key: ${this.apiConfig.apiKey.substring(0, 10)}...`);
          break;

        case 'help':
          await this.showHelp();
          break;

        case 'exit':
          console.log("👋 Closing browser and exiting...");
          await this.stagehand.close();
          this.rl.close();
          process.exit(0);
          break;

        default:
          console.log(`❌ Unknown command: ${cmd}`);
          console.log("💡 Type 'help' for available commands");
          break;
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  private async executeNaturalLanguageCommand(command: string) {
    if (!this.hasApiKey) {
      console.log("❌ Natural language commands require API key.");
      return;
    }

    try {
      console.log(`🤖 ${this.apiConfig.type} processing: "${command}"`);

      const lowerCommand = command.toLowerCase();

      // 智能导航
      if (lowerCommand.includes('go to') || lowerCommand.includes('navigate') || lowerCommand.includes('visit')) {
        const sitePatterns = [
          { pattern: /github/i, url: 'https://github.com' },
          { pattern: /google/i, url: 'https://google.com' },
          { pattern: /youtube/i, url: 'https://youtube.com' },
          { pattern: /python.*doc/i, url: 'https://docs.python.org' },
          { pattern: /stackoverflow/i, url: 'https://stackoverflow.com' },
        ];

        for (const site of sitePatterns) {
          if (site.pattern.test(command)) {
            console.log(`🌐 Smart navigation to: ${site.url}`);
            await this.page.goto(site.url);
            console.log("✅ Navigation completed");
            return;
          }
        }
      }

      // 信息提取
      if (lowerCommand.includes('extract') || lowerCommand.includes('get') || lowerCommand.includes('describe')) {
        console.log("📊 Extracting information...");
        const result = await this.page.extract({
          instruction: command,
          schema: z.object({
            result: z.string(),
          }),
        });
        console.log(`📋 Result: ${result.result}`);
        return;
      }

      // 页面观察
      if (lowerCommand.includes('find') || lowerCommand.includes('show') || lowerCommand.includes('list')) {
        console.log("👁️ Observing page...");
        const observations = await this.page.observe({
          instruction: command
        });
        console.log("🔍 Found these elements:");
        observations.slice(0, 6).forEach((obs: any, i: number) => {
          console.log(`   ${i + 1}. ${obs.description}`);
        });
        return;
      }

      // 执行动作
      console.log("🎯 Executing action...");
      await this.page.act(command);
      console.log("✅ Action completed");

    } catch (error) {
      console.log(`❌ ${this.apiConfig.type} Error: ${error.message}`);
      console.log("💡 Try a more specific command or use basic syntax");
    }
  }

  async start() {
    await this.init();
    
    if (!this.stagehand || !this.page) {
      console.log("❌ Failed to initialize browser. Exiting...");
      this.rl.close();
      return;
    }
    
    while (true) {
      const command = await this.question('🔗 openai-compat> ');
      
      if (command.trim() === '') continue;
      if (command.trim() === 'exit') {
        await this.executeBasicCommand('exit');
        break;
      }

      if (this.isBasicCommand(command)) {
        await this.executeBasicCommand(command);
      } else {
        await this.executeNaturalLanguageCommand(command);
      }
    }
  }
}

// 启动 OpenAI 兼容控制
if (import.meta.url === `file://${process.argv[1]}`) {
  const controller = new OpenAICompatibleController();
  controller.start().catch(console.error);
}
