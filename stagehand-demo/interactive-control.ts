#!/usr/bin/env tsx

import { createStagehand, checkApi<PERSON>ey } from './config.js';
import { z } from 'zod';
import * as readline from 'readline';

class InteractiveStagehand {
  private stagehand: any;
  private page: any;
  private rl: readline.Interface;
  private hasApiKey: boolean;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.hasApiKey = checkApiKey();
  }

  async init() {
    console.log("🎭 Stagehand Interactive Control");
    console.log("================================");
    console.log(`🔑 AI Features: ${this.hasApiKey ? '✅ Available' : '❌ Disabled (no API key)'}`);
    console.log("🌐 Ready to control any website!\n");

    this.stagehand = createStagehand();
    await this.stagehand.init();
    this.page = this.stagehand.page;
    
    console.log("✅ Browser initialized and ready!");
    console.log("💡 Type 'help' for available commands\n");
  }

  private question(prompt: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  private async showHelp() {
    console.log("\n📖 Available Commands:");
    console.log("======================");
    console.log("🌐 Navigation:");
    console.log("  goto <url>           - Navigate to a website");
    console.log("  back                 - Go back");
    console.log("  forward              - Go forward");
    console.log("  refresh              - Refresh page");
    console.log("  url                  - Show current URL");
    console.log("  title                - Show page title");
    
    console.log("\n🔍 Basic Actions:");
    console.log("  click <selector>     - Click an element");
    console.log("  type <selector> <text> - Type text into element");
    console.log("  fill <selector> <text> - Fill input with text");
    console.log("  clear <selector>     - Clear input field");
    console.log("  scroll <direction>   - Scroll (up/down/top/bottom)");
    
    console.log("\n📊 Information:");
    console.log("  text <selector>      - Get element text");
    console.log("  value <selector>     - Get input value");
    console.log("  visible <selector>   - Check if element is visible");
    console.log("  screenshot [name]    - Take a screenshot");
    
    if (this.hasApiKey) {
      console.log("\n🤖 AI Commands:");
      console.log("  ai-act <instruction> - AI-powered action");
      console.log("  ai-observe [instruction] - AI page observation");
      console.log("  ai-extract <instruction> - AI data extraction");
    }
    
    console.log("\n🛠️ Utility:");
    console.log("  wait <ms>            - Wait for specified milliseconds");
    console.log("  viewport <w> <h>     - Set viewport size");
    console.log("  help                 - Show this help");
    console.log("  exit                 - Close browser and exit");
    console.log("\n💡 Examples:");
    console.log("  goto https://google.com");
    console.log("  click input[name='q']");
    console.log("  type input[name='q'] hello world");
    if (this.hasApiKey) {
      console.log("  ai-act click the search button");
      console.log("  ai-extract get all search results");
    }
    console.log("");
  }

  private async executeCommand(command: string) {
    const parts = command.trim().split(' ');
    const cmd = parts[0].toLowerCase();
    const args = parts.slice(1);

    try {
      switch (cmd) {
        case 'help':
          await this.showHelp();
          break;

        case 'goto':
          if (args.length === 0) {
            console.log("❌ Usage: goto <url>");
            break;
          }
          let url = args.join(' ');
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }
          console.log(`🌐 Navigating to: ${url}`);
          await this.page.goto(url);
          console.log("✅ Navigation completed");
          break;

        case 'back':
          console.log("⬅️ Going back...");
          await this.page.goBack();
          console.log("✅ Went back");
          break;

        case 'forward':
          console.log("➡️ Going forward...");
          await this.page.goForward();
          console.log("✅ Went forward");
          break;

        case 'refresh':
          console.log("🔄 Refreshing page...");
          await this.page.reload();
          console.log("✅ Page refreshed");
          break;

        case 'url':
          console.log(`🔗 Current URL: ${this.page.url()}`);
          break;

        case 'title':
          const title = await this.page.title();
          console.log(`📄 Page title: ${title}`);
          break;

        case 'click':
          if (args.length === 0) {
            console.log("❌ Usage: click <selector>");
            break;
          }
          const clickSelector = args.join(' ');
          console.log(`🖱️ Clicking: ${clickSelector}`);
          await this.page.click(clickSelector);
          console.log("✅ Clicked");
          break;

        case 'type':
          if (args.length < 2) {
            console.log("❌ Usage: type <selector> <text>");
            break;
          }
          const typeSelector = args[0];
          const typeText = args.slice(1).join(' ');
          console.log(`⌨️ Typing "${typeText}" into: ${typeSelector}`);
          await this.page.type(typeSelector, typeText);
          console.log("✅ Text typed");
          break;

        case 'fill':
          if (args.length < 2) {
            console.log("❌ Usage: fill <selector> <text>");
            break;
          }
          const fillSelector = args[0];
          const fillText = args.slice(1).join(' ');
          console.log(`📝 Filling "${fillText}" into: ${fillSelector}`);
          await this.page.fill(fillSelector, fillText);
          console.log("✅ Field filled");
          break;

        case 'clear':
          if (args.length === 0) {
            console.log("❌ Usage: clear <selector>");
            break;
          }
          const clearSelector = args.join(' ');
          console.log(`🧹 Clearing: ${clearSelector}`);
          await this.page.fill(clearSelector, '');
          console.log("✅ Field cleared");
          break;

        case 'scroll':
          const direction = args[0] || 'down';
          console.log(`📜 Scrolling: ${direction}`);
          switch (direction.toLowerCase()) {
            case 'up':
              await this.page.keyboard.press('PageUp');
              break;
            case 'down':
              await this.page.keyboard.press('PageDown');
              break;
            case 'top':
              await this.page.keyboard.press('Home');
              break;
            case 'bottom':
              await this.page.keyboard.press('End');
              break;
            default:
              console.log("❌ Usage: scroll <up|down|top|bottom>");
              return;
          }
          console.log("✅ Scrolled");
          break;

        case 'text':
          if (args.length === 0) {
            console.log("❌ Usage: text <selector>");
            break;
          }
          const textSelector = args.join(' ');
          const text = await this.page.textContent(textSelector);
          console.log(`📝 Text: "${text}"`);
          break;

        case 'value':
          if (args.length === 0) {
            console.log("❌ Usage: value <selector>");
            break;
          }
          const valueSelector = args.join(' ');
          const value = await this.page.inputValue(valueSelector);
          console.log(`💾 Value: "${value}"`);
          break;

        case 'visible':
          if (args.length === 0) {
            console.log("❌ Usage: visible <selector>");
            break;
          }
          const visibleSelector = args.join(' ');
          const isVisible = await this.page.isVisible(visibleSelector);
          console.log(`👁️ Visible: ${isVisible}`);
          break;

        case 'screenshot':
          const filename = args[0] || `screenshot-${Date.now()}.png`;
          console.log(`📸 Taking screenshot: ${filename}`);
          await this.page.screenshot({ path: filename });
          console.log(`✅ Screenshot saved: ${filename}`);
          break;

        case 'wait':
          const ms = parseInt(args[0]) || 1000;
          console.log(`⏳ Waiting ${ms}ms...`);
          await this.page.waitForTimeout(ms);
          console.log("✅ Wait completed");
          break;

        case 'viewport':
          if (args.length < 2) {
            console.log("❌ Usage: viewport <width> <height>");
            break;
          }
          const width = parseInt(args[0]);
          const height = parseInt(args[1]);
          console.log(`📱 Setting viewport: ${width}x${height}`);
          await this.page.setViewportSize({ width, height });
          console.log("✅ Viewport set");
          break;

        // AI Commands
        case 'ai-act':
          if (!this.hasApiKey) {
            console.log("❌ AI features require an API key. Set OPENAI_API_KEY in .env");
            break;
          }
          if (args.length === 0) {
            console.log("❌ Usage: ai-act <instruction>");
            break;
          }
          const aiAction = args.join(' ');
          console.log(`🤖 AI Action: ${aiAction}`);
          await this.page.act(aiAction);
          console.log("✅ AI action completed");
          break;

        case 'ai-observe':
          if (!this.hasApiKey) {
            console.log("❌ AI features require an API key. Set OPENAI_API_KEY in .env");
            break;
          }
          const aiInstruction = args.length > 0 ? args.join(' ') : "Find interactive elements on this page";
          console.log(`🤖 AI Observing: ${aiInstruction}`);
          const observations = await this.page.observe({ instruction: aiInstruction });
          console.log("🔍 AI found these elements:");
          observations.slice(0, 10).forEach((obs: any, i: number) => {
            console.log(`   ${i + 1}. ${obs.description}`);
          });
          break;

        case 'ai-extract':
          if (!this.hasApiKey) {
            console.log("❌ AI features require an API key. Set OPENAI_API_KEY in .env");
            break;
          }
          if (args.length === 0) {
            console.log("❌ Usage: ai-extract <instruction>");
            break;
          }
          const extractInstruction = args.join(' ');
          console.log(`🤖 AI Extracting: ${extractInstruction}`);
          const result = await this.page.extract({
            instruction: extractInstruction,
            schema: z.object({
              data: z.string(),
            }),
          });
          console.log(`📊 AI Result: ${result.data}`);
          break;

        case 'exit':
          console.log("👋 Closing browser and exiting...");
          await this.stagehand.close();
          this.rl.close();
          process.exit(0);
          break;

        case '':
          break;

        default:
          console.log(`❌ Unknown command: ${cmd}`);
          console.log("💡 Type 'help' for available commands");
          break;
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  async start() {
    await this.init();
    
    while (true) {
      const command = await this.question('🎭 stagehand> ');
      if (command.trim() === 'exit') {
        await this.executeCommand('exit');
        break;
      }
      await this.executeCommand(command);
    }
  }
}

// 启动交互式控制
if (import.meta.url === `file://${process.argv[1]}`) {
  const controller = new InteractiveStagehand();
  controller.start().catch(console.error);
}
