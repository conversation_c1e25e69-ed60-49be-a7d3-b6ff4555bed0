# 🚀 Stagehand 快速开始指南

## 📦 立即运行 (30 秒)

```bash
# 进入演示目录
cd stagehand-demo

# 运行增强演示 (推荐)
npm run enhanced

# 或运行基础演示
npm run basic

# 或运行测试套件
npm test
```

## 🎯 推荐演示顺序

### 1️⃣ 增强演示 (必看)
```bash
npm run enhanced
```
**看点**: 完整的浏览器自动化展示，包括响应式测试、性能分析等

### 2️⃣ 测试套件
```bash
npm test
```
**看点**: 验证所有功能是否正常工作

### 3️⃣ 示例集合
```bash
npm run examples
```
**看点**: 多种使用场景的演示

## 🔑 AI 功能设置 (可选)

如果您想体验 AI 驱动的自动化：

```bash
# 1. 获取 OpenAI API Key
# 访问: https://platform.openai.com/api-keys

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，添加: OPENAI_API_KEY=sk-your-key-here

# 3. 运行 AI 演示
npm run ai
```

## 📋 所有可用命令

| 命令 | 描述 | 时间 | API Key |
|------|------|------|---------|
| `npm run enhanced` | 🌟 增强演示 | ~2分钟 | ❌ |
| `npm run basic` | 基础演示 | ~30秒 | ❌ |
| `npm test` | 测试套件 | ~1分钟 | ❌ |
| `npm run examples` | 示例集合 | ~1分钟 | ❌ |
| `npm run ai` | AI 演示 | ~2分钟 | ✅ |
| `npm run local` | 网站测试 | ~1分钟 | ❌ |

## 🛠️ 自定义和扩展

### 修改测试页面
编辑 `test.html` 来测试不同的网页元素和交互

### 创建新演示
复制任意 `*-demo.ts` 文件，修改其中的逻辑

### 集成到项目
参考演示代码的模式，集成到您的 CUTE 项目中

## ❓ 常见问题

**Q: 浏览器没有启动？**
```bash
npm run install-browsers
```

**Q: 想要无头模式运行？**
编辑 `.env` 文件: `DEMO_HEADLESS=true`

**Q: 想要更详细的日志？**
编辑 `.env` 文件: `DEMO_VERBOSE=2`

**Q: AI 功能报错？**
确保 API key 正确设置在 `.env` 文件中

## 🎉 下一步

1. **体验完整功能**: 运行所有演示
2. **获取 API key**: 解锁 AI 功能  
3. **自定义测试**: 修改 test.html
4. **集成项目**: 应用到 CUTE 项目

---

**开始您的 Stagehand 之旅！** 🎭✨
