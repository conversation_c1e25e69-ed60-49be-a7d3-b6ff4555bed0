#!/usr/bin/env tsx

import { createStagehand, checkApi<PERSON>ey } from './config.js';
import { z } from 'zod';
import * as readline from 'readline';

class GeminiController {
  private stagehand: any;
  private page: any;
  private rl: readline.Interface;
  private hasApiKey: boolean;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.hasApiKey = checkApiKey();
  }

  async init() {
    console.log("🤖 Stagehand + Gemini AI Control");
    console.log("=================================");
    console.log(`🔑 Gemini API Key: ${process.env.GOOGLE_API_KEY ? '✅ Found' : '❌ Not found'}`);
    
    if (!this.hasApiKey) {
      console.log("\n❌ No API key found!");
      console.log("💡 Your Gemini API key should be set in .env file");
      console.log("🔧 Falling back to basic commands only");
    } else {
      console.log("✅ Gemini AI Features Available!");
      console.log("🌟 You can now use natural language commands!");
    }

    try {
      this.stagehand = createStagehand();
      await this.stagehand.init();
      this.page = this.stagehand.page;
      
      console.log("✅ Browser initialized and ready!");
      console.log("💬 Start typing your commands...\n");
      
      if (this.hasApiKey) {
        console.log("🎯 Try these natural language commands:");
        console.log("  'Go to Google'");
        console.log("  'Search for Stagehand browser automation'");
        console.log("  'Click the first result'");
        console.log("  'Get the page title'");
        console.log("");
      }
    } catch (error) {
      console.error("❌ Initialization error:", error.message);
      if (error.message.includes('location')) {
        console.log("\n🌍 Gemini API地区限制解决方案:");
        console.log("1. 尝试使用VPN连接到支持的地区");
        console.log("2. 或者获取OpenAI API key作为替代");
        console.log("3. 当前可以使用基础命令进行控制");
      }
    }
  }

  private question(prompt: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  private async showHelp() {
    console.log("\n📖 Available Commands:");
    console.log("======================");
    
    if (this.hasApiKey) {
      console.log("🤖 Natural Language Commands (Gemini AI):");
      console.log("  'Go to [website]'        - Navigate to website");
      console.log("  'Search for [query]'     - Perform search");
      console.log("  'Click [description]'    - Click element");
      console.log("  'Type [text] in [field]' - Fill form field");
      console.log("  'Get [information]'      - Extract data");
      console.log("  'Find all [elements]'    - Discover elements");
      console.log("");
      console.log("💡 Examples:");
      console.log("  'Go to GitHub'");
      console.log("  'Search for machine learning'");
      console.log("  'Click the login button'");
      console.log("  'Type <EMAIL> in email field'");
      console.log("  'Get all the links on this page'");
      console.log("");
    }
    
    console.log("🔧 Basic Commands (Always Available):");
    console.log("  goto <url>              - Navigate to URL");
    console.log("  click <selector>        - Click element");
    console.log("  type <selector> <text>  - Type text");
    console.log("  title                   - Get page title");
    console.log("  url                     - Get current URL");
    console.log("  screenshot [filename]   - Take screenshot");
    console.log("  wait <ms>              - Wait milliseconds");
    console.log("  help                   - Show this help");
    console.log("  exit                   - Close and exit");
    console.log("");
  }

  private isBasicCommand(command: string): boolean {
    const basicCommands = ['goto', 'click', 'type', 'fill', 'clear', 'title', 'url', 'screenshot', 'wait', 'help', 'exit'];
    const firstWord = command.trim().split(' ')[0].toLowerCase();
    return basicCommands.includes(firstWord);
  }

  private async executeBasicCommand(command: string) {
    const parts = command.trim().split(' ');
    const cmd = parts[0].toLowerCase();
    const args = parts.slice(1);

    try {
      switch (cmd) {
        case 'goto':
          if (args.length === 0) {
            console.log("❌ Usage: goto <url>");
            break;
          }
          let url = args.join(' ');
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }
          console.log(`🌐 Navigating to: ${url}`);
          await this.page.goto(url);
          console.log("✅ Navigation completed");
          break;

        case 'click':
          if (args.length === 0) {
            console.log("❌ Usage: click <selector>");
            break;
          }
          const selector = args.join(' ');
          console.log(`🖱️ Clicking: ${selector}`);
          await this.page.click(selector);
          console.log("✅ Clicked");
          break;

        case 'type':
          if (args.length < 2) {
            console.log("❌ Usage: type <selector> <text>");
            break;
          }
          const typeSelector = args[0];
          const typeText = args.slice(1).join(' ');
          console.log(`⌨️ Typing "${typeText}" into: ${typeSelector}`);
          await this.page.type(typeSelector, typeText);
          console.log("✅ Text typed");
          break;

        case 'title':
          const title = await this.page.title();
          console.log(`📄 Page title: ${title}`);
          break;

        case 'url':
          console.log(`🔗 Current URL: ${this.page.url()}`);
          break;

        case 'screenshot':
          const filename = args[0] || `screenshot-${Date.now()}.png`;
          console.log(`📸 Taking screenshot: ${filename}`);
          await this.page.screenshot({ path: filename });
          console.log(`✅ Screenshot saved: ${filename}`);
          break;

        case 'wait':
          const ms = parseInt(args[0]) || 1000;
          console.log(`⏳ Waiting ${ms}ms...`);
          await this.page.waitForTimeout(ms);
          console.log("✅ Wait completed");
          break;

        case 'help':
          await this.showHelp();
          break;

        case 'exit':
          console.log("👋 Closing browser and exiting...");
          await this.stagehand.close();
          this.rl.close();
          process.exit(0);
          break;

        default:
          console.log(`❌ Unknown command: ${cmd}`);
          console.log("💡 Type 'help' for available commands");
          break;
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  private async executeNaturalLanguageCommand(command: string) {
    if (!this.hasApiKey) {
      console.log("❌ Natural language commands require Gemini API key.");
      console.log("💡 Use basic commands like: goto google.com");
      return;
    }

    try {
      console.log(`🤖 Gemini AI processing: "${command}"`);

      // 智能判断命令类型
      const lowerCommand = command.toLowerCase();

      // 导航命令
      if (lowerCommand.includes('go to') || lowerCommand.includes('navigate to') || lowerCommand.includes('visit')) {
        const urlMatch = command.match(/(?:go to|navigate to|visit)\s+(.+)/i);
        if (urlMatch) {
          let url = urlMatch[1].trim();
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            if (!url.includes('.')) {
              url = url + '.com';
            }
            url = 'https://' + url;
          }
          console.log(`🌐 Navigating to: ${url}`);
          await this.page.goto(url);
          console.log("✅ Navigation completed");
          return;
        }
      }

      // 信息提取命令
      if (lowerCommand.includes('get') || lowerCommand.includes('extract') || lowerCommand.includes('find') || lowerCommand.includes('what')) {
        console.log("📊 Extracting information with Gemini AI...");
        const result = await this.page.extract({
          instruction: command,
          schema: z.object({
            result: z.string(),
          }),
        });
        console.log(`📋 Gemini Result: ${result.result}`);
        return;
      }

      // 观察命令
      if (lowerCommand.includes('show') || lowerCommand.includes('list') || lowerCommand.includes('observe')) {
        console.log("👁️ Observing page with Gemini AI...");
        const observations = await this.page.observe({
          instruction: command
        });
        console.log("🔍 Gemini found these elements:");
        observations.slice(0, 5).forEach((obs: any, i: number) => {
          console.log(`   ${i + 1}. ${obs.description}`);
        });
        return;
      }

      // 其他动作命令
      console.log("🎯 Executing action with Gemini AI...");
      await this.page.act(command);
      console.log("✅ Gemini action completed");

    } catch (error) {
      console.log(`❌ Gemini Error: ${error.message}`);
      
      if (error.message.includes('location') || error.message.includes('region')) {
        console.log("🌍 地区限制问题 - 尝试基础命令或使用VPN");
      } else if (error.message.includes('quota') || error.message.includes('limit')) {
        console.log("📊 API配额限制 - 请检查您的Gemini API使用情况");
      } else {
        console.log("💡 Try a more specific command or use basic syntax");
      }
    }
  }

  async start() {
    await this.init();
    
    if (!this.stagehand || !this.page) {
      console.log("❌ Failed to initialize browser. Exiting...");
      this.rl.close();
      return;
    }
    
    while (true) {
      const command = await this.question('🤖 gemini> ');
      
      if (command.trim() === '') continue;
      if (command.trim() === 'exit') {
        await this.executeBasicCommand('exit');
        break;
      }

      // 判断是基础命令还是自然语言命令
      if (this.isBasicCommand(command)) {
        await this.executeBasicCommand(command);
      } else {
        await this.executeNaturalLanguageCommand(command);
      }
    }
  }
}

// 启动Gemini控制
if (import.meta.url === `file://${process.argv[1]}`) {
  const controller = new GeminiController();
  controller.start().catch(console.error);
}
