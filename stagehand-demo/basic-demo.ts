#!/usr/bin/env tsx

import { createStagehand } from './config.js';
import path from 'path';

async function basicDemo() {
  console.log("🎭 Stagehand Basic Demo");
  console.log("========================");
  console.log("This demo shows basic browser automation without AI features.\n");

  const stagehand = createStagehand();

  try {
    await stagehand.init();
    console.log("✅ Stagehand initialized successfully!");

    const page = stagehand.page;
    const testHtmlPath = path.join(process.cwd(), "test.html");
    const fileUrl = `file://${testHtmlPath}`;

    console.log("\n📍 Step 1: Loading test page...");
    await page.goto(fileUrl);
    
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);

    console.log("\n🔍 Step 2: Basic interactions...");
    
    // 输入文本
    console.log("✏️  Typing in search box...");
    await page.fill('#searchInput', 'Basic Demo Test');
    
    // 点击搜索
    console.log("🔍 Clicking search button...");
    await page.click('button:has-text("Search")');
    await page.waitForTimeout(1000);
    
    // 检查结果
    const resultVisible = await page.isVisible('#result');
    console.log(`📊 Search result visible: ${resultVisible}`);
    
    if (resultVisible) {
      const resultText = await page.textContent('#resultText');
      console.log(`📝 Result: ${resultText}`);
    }

    console.log("\n🧹 Step 3: Testing clear...");
    await page.click('button:has-text("Clear")');
    await page.waitForTimeout(500);
    
    const inputValue = await page.inputValue('#searchInput');
    console.log(`📝 Input after clear: "${inputValue}"`);

    console.log("\n💡 Step 4: Testing info dialog...");
    page.on('dialog', async dialog => {
      console.log(`🔔 Dialog: ${dialog.message()}`);
      await dialog.accept();
    });
    
    await page.click('button:has-text("Show Info")');
    await page.waitForTimeout(1000);

    console.log("\n🎉 Basic demo completed successfully!");
    console.log("💡 To try AI features, run: npm run ai");

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await stagehand.close();
    console.log("✅ Demo finished!");
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  basicDemo().catch(console.error);
}
