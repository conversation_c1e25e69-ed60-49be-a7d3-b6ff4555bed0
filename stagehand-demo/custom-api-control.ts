#!/usr/bin/env tsx

import { Stagehand } from '@browserbasehq/stagehand';
import { z } from 'zod';
import * as readline from 'readline';
import { config } from 'dotenv';

// 加载环境变量
config();

class CustomApiController {
  private stagehand: any;
  private page: any;
  private rl: readline.Interface;
  private hasApiKey: boolean;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    this.hasApiKey = !!(process.env.GOOGLE_API_KEY || process.env.OPENAI_API_KEY);
  }

  async init() {
    console.log("🎛️ Stagehand Custom API Control");
    console.log("================================");
    
    // 检查可用的 API
    const apiOptions = [];
    if (process.env.GOOGLE_API_KEY) {
      apiOptions.push("Google Gemini");
    }
    if (process.env.OPENAI_API_KEY) {
      apiOptions.push("OpenAI");
    }
    
    if (apiOptions.length === 0) {
      console.log("❌ No API keys found!");
      console.log("💡 Please set one of the following in .env:");
      console.log("   GOOGLE_API_KEY=your-gemini-key");
      console.log("   OPENAI_API_KEY=your-openai-key");
      return;
    }

    console.log(`🔑 Available APIs: ${apiOptions.join(', ')}`);

    try {
      // 优先使用 OpenAI (更稳定)
      let modelConfig;
      if (process.env.OPENAI_API_KEY) {
        modelConfig = {
          modelName: 'gpt-4o-mini',
          modelClientOptions: {
            apiKey: process.env.OPENAI_API_KEY,
          }
        };
        console.log("🤖 Using OpenAI GPT-4o-mini");
      } else if (process.env.GOOGLE_API_KEY) {
        modelConfig = {
          modelName: 'google/gemini-1.5-flash',
          modelClientOptions: {
            apiKey: process.env.GOOGLE_API_KEY,
          }
        };
        console.log("🤖 Using Google Gemini 1.5 Flash");
      }

      this.stagehand = new Stagehand({
        env: "LOCAL",
        verbose: 1,
        enableCaching: false,
        ...modelConfig,
        localBrowserLaunchOptions: {
          headless: process.env.DEMO_HEADLESS === 'true',
          viewport: {
            width: parseInt(process.env.DEMO_VIEWPORT_WIDTH || '1280'),
            height: parseInt(process.env.DEMO_VIEWPORT_HEIGHT || '720'),
          },
        },
      });

      await this.stagehand.init();
      this.page = this.stagehand.page;
      
      console.log("✅ Browser initialized successfully!");
      console.log("💬 Ready for natural language commands...\n");
      
      console.log("🎯 Try these commands:");
      console.log("  'Go to Google'");
      console.log("  'Navigate to GitHub'");
      console.log("  'Search for machine learning'");
      console.log("  'Click the search button'");
      console.log("  'Extract page title'");
      console.log("  'Find all links on this page'");
      console.log("");
      
    } catch (error) {
      console.error("❌ Initialization error:", error.message);
      if (error.message.includes('location')) {
        console.log("\n🌍 API 访问问题:");
        console.log("1. Gemini API 可能有地区限制");
        console.log("2. 建议使用 OpenAI API key");
        console.log("3. 或者配置代理访问");
      }
    }
  }

  private question(prompt: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  private async showHelp() {
    console.log("\n📖 Available Commands:");
    console.log("======================");
    
    if (this.hasApiKey) {
      console.log("🤖 Natural Language Commands:");
      console.log("  'Go to [website]'           - Navigate to website");
      console.log("  'Search for [query]'        - Perform search");
      console.log("  'Click [description]'       - Click element");
      console.log("  'Type [text] in [field]'    - Fill form");
      console.log("  'Extract [information]'     - Get data");
      console.log("  'Find all [elements]'       - Discover elements");
      console.log("  'Scroll to [position]'      - Scroll page");
      console.log("");
      console.log("💡 Examples:");
      console.log("  'Go to the Python documentation'");
      console.log("  'Search for artificial intelligence'");
      console.log("  'Click the login button'");
      console.log("  'Extract all the article titles'");
      console.log("  'Find all navigation links'");
      console.log("");
    }
    
    console.log("🔧 Basic Commands:");
    console.log("  goto <url>              - Navigate to URL");
    console.log("  click <selector>        - Click element");
    console.log("  type <selector> <text>  - Type text");
    console.log("  title                   - Get page title");
    console.log("  url                     - Get current URL");
    console.log("  screenshot [filename]   - Take screenshot");
    console.log("  help                    - Show this help");
    console.log("  exit                    - Close and exit");
    console.log("");
  }

  private isBasicCommand(command: string): boolean {
    const basicCommands = ['goto', 'click', 'type', 'fill', 'clear', 'title', 'url', 'screenshot', 'wait', 'help', 'exit'];
    const firstWord = command.trim().split(' ')[0].toLowerCase();
    return basicCommands.includes(firstWord);
  }

  private async executeBasicCommand(command: string) {
    const parts = command.trim().split(' ');
    const cmd = parts[0].toLowerCase();
    const args = parts.slice(1);

    try {
      switch (cmd) {
        case 'goto':
          if (args.length === 0) {
            console.log("❌ Usage: goto <url>");
            break;
          }
          let url = args.join(' ');
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }
          console.log(`🌐 Navigating to: ${url}`);
          await this.page.goto(url);
          console.log("✅ Navigation completed");
          break;

        case 'click':
          if (args.length === 0) {
            console.log("❌ Usage: click <selector>");
            break;
          }
          const selector = args.join(' ');
          console.log(`🖱️ Clicking: ${selector}`);
          await this.page.click(selector);
          console.log("✅ Clicked");
          break;

        case 'type':
          if (args.length < 2) {
            console.log("❌ Usage: type <selector> <text>");
            break;
          }
          const typeSelector = args[0];
          const typeText = args.slice(1).join(' ');
          console.log(`⌨️ Typing "${typeText}" into: ${typeSelector}`);
          await this.page.type(typeSelector, typeText);
          console.log("✅ Text typed");
          break;

        case 'title':
          const title = await this.page.title();
          console.log(`📄 Page title: ${title}`);
          break;

        case 'url':
          console.log(`🔗 Current URL: ${this.page.url()}`);
          break;

        case 'screenshot':
          const filename = args[0] || `custom-api-screenshot-${Date.now()}.png`;
          console.log(`📸 Taking screenshot: ${filename}`);
          await this.page.screenshot({ path: filename });
          console.log(`✅ Screenshot saved: ${filename}`);
          break;

        case 'help':
          await this.showHelp();
          break;

        case 'exit':
          console.log("👋 Closing browser and exiting...");
          await this.stagehand.close();
          this.rl.close();
          process.exit(0);
          break;

        default:
          console.log(`❌ Unknown command: ${cmd}`);
          console.log("💡 Type 'help' for available commands");
          break;
      }
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }

  private async executeNaturalLanguageCommand(command: string) {
    if (!this.hasApiKey) {
      console.log("❌ Natural language commands require API key.");
      return;
    }

    try {
      console.log(`🤖 AI processing: "${command}"`);

      const lowerCommand = command.toLowerCase();

      // 智能导航
      if (lowerCommand.includes('go to') || lowerCommand.includes('navigate') || lowerCommand.includes('visit')) {
        const sitePatterns = [
          { pattern: /github/i, url: 'https://github.com' },
          { pattern: /google/i, url: 'https://google.com' },
          { pattern: /youtube/i, url: 'https://youtube.com' },
          { pattern: /python.*doc/i, url: 'https://docs.python.org' },
          { pattern: /stackoverflow/i, url: 'https://stackoverflow.com' },
          { pattern: /mdn/i, url: 'https://developer.mozilla.org' },
        ];

        for (const site of sitePatterns) {
          if (site.pattern.test(command)) {
            console.log(`🌐 Smart navigation to: ${site.url}`);
            await this.page.goto(site.url);
            console.log("✅ Navigation completed");
            return;
          }
        }
      }

      // 信息提取
      if (lowerCommand.includes('extract') || lowerCommand.includes('get') || lowerCommand.includes('title')) {
        console.log("📊 Extracting information...");
        const result = await this.page.extract({
          instruction: command,
          schema: z.object({
            result: z.string(),
          }),
        });
        console.log(`📋 AI Result: ${result.result}`);
        return;
      }

      // 页面观察
      if (lowerCommand.includes('find') || lowerCommand.includes('show') || lowerCommand.includes('list')) {
        console.log("👁️ Observing page...");
        const observations = await this.page.observe({
          instruction: command
        });
        console.log("🔍 AI found these elements:");
        observations.slice(0, 6).forEach((obs: any, i: number) => {
          console.log(`   ${i + 1}. ${obs.description}`);
        });
        return;
      }

      // 执行动作
      console.log("🎯 Executing action...");
      await this.page.act(command);
      console.log("✅ AI action completed");

    } catch (error) {
      console.log(`❌ AI Error: ${error.message}`);
      if (error.message.includes('location')) {
        console.log("🌍 地区限制问题 - 建议使用 OpenAI API");
      } else {
        console.log("💡 Try a more specific command or use basic syntax");
      }
    }
  }

  async start() {
    await this.init();
    
    if (!this.stagehand || !this.page) {
      console.log("❌ Failed to initialize browser. Exiting...");
      this.rl.close();
      return;
    }
    
    while (true) {
      const command = await this.question('🎛️ custom> ');
      
      if (command.trim() === '') continue;
      if (command.trim() === 'exit') {
        await this.executeBasicCommand('exit');
        break;
      }

      if (this.isBasicCommand(command)) {
        await this.executeBasicCommand(command);
      } else {
        await this.executeNaturalLanguageCommand(command);
      }
    }
  }
}

// 启动自定义 API 控制
if (import.meta.url === `file://${process.argv[1]}`) {
  const controller = new CustomApiController();
  controller.start().catch(console.error);
}
