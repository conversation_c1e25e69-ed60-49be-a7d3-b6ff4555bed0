#!/usr/bin/env tsx

import { createStagehand, checkApi<PERSON>ey } from './config.js';

async function testAll() {
  console.log("🧪 Stagehand Test Suite");
  console.log("=======================");
  console.log("Running comprehensive tests...\n");

  const hasApiKey = checkApiKey();
  console.log(`🔑 API Key available: ${hasApiKey ? '✅ Yes' : '❌ No'}`);
  
  let passedTests = 0;
  let totalTests = 0;

  function test(name: string, fn: () => Promise<void>) {
    return async () => {
      totalTests++;
      try {
        console.log(`\n🧪 Test: ${name}`);
        await fn();
        console.log(`✅ PASS: ${name}`);
        passedTests++;
      } catch (error) {
        console.log(`❌ FAIL: ${name}`);
        console.log(`   Error: ${error.message}`);
      }
    };
  }

  const stagehand = createStagehand();

  try {
    await stagehand.init();
    console.log("✅ Stagehand initialized for testing");

    const page = stagehand.page;
    const path = await import('path');
    const testHtmlPath = path.join(process.cwd(), "test.html");
    const fileUrl = `file://${testHtmlPath}`;

    await page.goto(fileUrl);

    // Test 1: Basic Navigation
    await test("Basic Navigation", async () => {
      const title = await page.title();
      if (!title.includes('Stagehand')) {
        throw new Error('Page title not as expected');
      }
    })();

    // Test 2: Element Interaction
    await test("Element Interaction", async () => {
      await page.fill('#searchInput', 'Test input');
      const value = await page.inputValue('#searchInput');
      if (value !== 'Test input') {
        throw new Error('Input value not set correctly');
      }
    })();

    // Test 3: Button Clicking
    await test("Button Clicking", async () => {
      await page.click('button:has-text("Search")');
      await page.waitForTimeout(500);
      const resultVisible = await page.isVisible('#result');
      if (!resultVisible) {
        throw new Error('Search result not visible after clicking');
      }
    })();

    // Test 4: Text Extraction
    await test("Text Extraction", async () => {
      const resultText = await page.textContent('#resultText');
      if (!resultText || !resultText.includes('Test input')) {
        throw new Error('Result text not as expected');
      }
    })();

    // Test 5: Clear Functionality
    await test("Clear Functionality", async () => {
      await page.click('button:has-text("Clear")');
      await page.waitForTimeout(500);
      const inputValue = await page.inputValue('#searchInput');
      if (inputValue !== '') {
        throw new Error('Input not cleared properly');
      }
    })();

    // Test 6: Dialog Handling
    await test("Dialog Handling", async () => {
      let dialogHandled = false;
      page.on('dialog', async dialog => {
        dialogHandled = true;
        await dialog.accept();
      });
      
      await page.click('button:has-text("Show Info")');
      await page.waitForTimeout(1000);
      
      if (!dialogHandled) {
        throw new Error('Dialog was not handled');
      }
    })();

    // AI Tests (only if API key is available)
    if (hasApiKey) {
      // Test 7: AI Observation
      await test("AI Observation", async () => {
        const observations = await page.observe({
          instruction: "Find interactive elements"
        });
        if (!observations || observations.length === 0) {
          throw new Error('AI observation returned no results');
        }
      })();

      // Test 8: AI Action
      await test("AI Action", async () => {
        await page.act("Type 'AI test' in the search input");
        const value = await page.inputValue('#searchInput');
        if (!value.includes('AI test')) {
          throw new Error('AI action did not work as expected');
        }
      })();

      // Test 9: AI Extraction
      await test("AI Extraction", async () => {
        const { z } = await import('zod');
        await page.act("Click the search button");
        await page.waitForTimeout(500);
        
        const result = await page.extract({
          instruction: "Get the search result text",
          schema: z.object({ text: z.string() }),
        });
        
        if (!result.text || !result.text.includes('AI test')) {
          throw new Error('AI extraction did not work as expected');
        }
      })();
    } else {
      console.log("\n⏭️  Skipping AI tests (no API key)");
    }

    // Summary
    console.log("\n📊 Test Results");
    console.log("===============");
    console.log(`✅ Passed: ${passedTests}/${totalTests}`);
    console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
    console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

    if (passedTests === totalTests) {
      console.log("\n🎉 All tests passed! Stagehand is working perfectly!");
    } else {
      console.log("\n⚠️  Some tests failed. Check the errors above.");
    }

    if (!hasApiKey) {
      console.log("\n💡 To test AI features, set up your API key:");
      console.log("   cp .env.example .env");
      console.log("   # Edit .env with your API key");
      console.log("   npm test");
    }

  } catch (error) {
    console.error("❌ Test suite error:", error);
  } finally {
    await stagehand.close();
    console.log("\n✅ Test suite finished!");
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  testAll().catch(console.error);
}
