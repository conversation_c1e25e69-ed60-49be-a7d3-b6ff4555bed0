#!/bin/bash

# Stagehand with Proxy for Gemini API
# ===================================

echo "🌐 Starting Stagehand with Proxy for Gemini API"
echo "================================================"

# 设置代理环境变量
export https_proxy=http://127.0.0.1:7897
export http_proxy=http://127.0.0.1:7897
export all_proxy=socks5://127.0.0.1:7897

echo "✅ Proxy configured:"
echo "   https_proxy=$https_proxy"
echo "   http_proxy=$http_proxy"
echo "   all_proxy=$all_proxy"
echo ""

# 检查代理是否可用
echo "🔍 Testing proxy connection..."
if curl -s --proxy $https_proxy --max-time 5 https://www.google.com > /dev/null; then
    echo "✅ Proxy connection successful!"
else
    echo "⚠️  Proxy connection failed - please check your proxy settings"
    echo "💡 Make sure your proxy is running on 127.0.0.1:7897"
fi

echo ""
echo "🚀 Starting Gemini control..."
echo "💡 You can now use natural language commands with Gemini AI!"
echo ""

# 启动 Gemini 控制
npm run gemini
