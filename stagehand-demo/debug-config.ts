#!/usr/bin/env tsx

import { Stagehand } from '@browserbasehq/stagehand';
import { config } from 'dotenv';
import { z } from 'zod';
import path from 'path';

config();

async function debugConfig() {
  console.log("🔍 Debugging Stagehand Configuration");
  console.log("====================================");
  
  console.log("Environment variables:");
  console.log(`OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? 'SET' : 'NOT SET'}`);
  console.log(`OPENAI_BASE_URL: ${process.env.OPENAI_BASE_URL || 'NOT SET'}`);
  console.log("");

  // 测试不同的配置方式
  const configs = [
    {
      name: "Method 1: Standard OpenAI Compatible",
      config: {
        env: "LOCAL" as const,
        verbose: 2,
        enableCaching: false,
        modelName: "gpt-4o-mini",
        modelClientOptions: {
          apiKey: process.env.OPENAI_API_KEY,
          baseURL: process.env.OPENAI_BASE_URL,
        },
        localBrowserLaunchOptions: {
          headless: true,
          viewport: { width: 1280, height: 720 },
        },
      }
    },
    {
      name: "Method 2: Explicit Provider",
      config: {
        env: "LOCAL" as const,
        verbose: 2,
        enableCaching: false,
        modelName: "gemini-2.5-flash-preview-05-20",
        modelClientOptions: {
          apiKey: process.env.OPENAI_API_KEY,
          baseURL: process.env.OPENAI_BASE_URL,
          provider: "openai",
        },
        localBrowserLaunchOptions: {
          headless: true,
          viewport: { width: 1280, height: 720 },
        },
      }
    },
    {
      name: "Method 3: Direct OpenAI Format",
      config: {
        env: "LOCAL" as const,
        verbose: 2,
        enableCaching: false,
        modelName: "gemini-2.5-flash-preview-05-20",
        openaiAPIKey: process.env.OPENAI_API_KEY,
        openaiBaseURL: process.env.OPENAI_BASE_URL,
        localBrowserLaunchOptions: {
          headless: true,
          viewport: { width: 1280, height: 720 },
        },
      }
    }
  ];

  for (const testConfig of configs) {
    console.log(`\n🧪 Testing: ${testConfig.name}`);
    console.log("=" + "=".repeat(testConfig.name.length + 10));
    
    let stagehand;
    try {
      console.log("Creating Stagehand instance...");
      stagehand = new Stagehand(testConfig.config);
      
      console.log("Initializing...");
      await stagehand.init();
      console.log("✅ Initialization: SUCCESS");

      const page = stagehand.page;
      
      console.log("Loading test page...");
      const testHtmlPath = path.join(process.cwd(), "test.html");
      const fileUrl = `file://${testHtmlPath}`;
      await page.goto(fileUrl);
      console.log("✅ Navigation: SUCCESS");

      console.log("Testing AI extract...");
      try {
        const result = await page.extract({
          instruction: "Get the page title",
          schema: z.object({
            title: z.string(),
          }),
        });
        console.log(`✅ AI Extract: SUCCESS - Title: ${result.title}`);
        
        // 如果成功，这就是正确的配置
        console.log("\n🎉 FOUND WORKING CONFIGURATION!");
        console.log("Use this configuration in your main script.");
        
      } catch (aiError) {
        console.log(`❌ AI Extract: FAILED - ${aiError.message}`);
      }

      await stagehand.close();
      console.log("✅ Cleanup: SUCCESS");

    } catch (error) {
      console.log(`❌ ${testConfig.name}: FAILED - ${error.message}`);
      if (stagehand) {
        try {
          await stagehand.close();
        } catch (closeError) {
          // Ignore cleanup errors
        }
      }
    }
  }

  console.log("\n📋 Summary");
  console.log("==========");
  console.log("Check which configuration method worked above.");
  console.log("Use that exact configuration in your main control script.");
}

debugConfig().catch(console.error);
