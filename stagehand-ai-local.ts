import { Stagehand } from "@browserbasehq/stagehand";
import { z } from "zod";
import path from "path";

async function aiLocalDemo() {
  console.log("🎭 Starting Stagehand AI demo with local file...");

  // 检查是否有 API key
  if (!process.env.OPENAI_API_KEY) {
    console.log("❌ OPENAI_API_KEY not found in environment variables");
    console.log("💡 To run this demo, please set your OpenAI API key:");
    console.log("   export OPENAI_API_KEY='your-api-key-here'");
    console.log("   pnpm run stagehand-ai-local");
    return;
  }

  const currentDir = process.cwd();
  const testHtmlPath = path.join(currentDir, "test.html");
  const fileUrl = `file://${testHtmlPath}`;

  // 初始化 Stagehand 实例
  const stagehand = new Stagehand({
    env: "LOCAL",
    verbose: 1,
    enableCaching: false,
    modelName: "gpt-4o-mini", // 使用更便宜的模型
    modelClientOptions: {
      apiKey: process.env.OPENAI_API_KEY,
    },
    localBrowserLaunchOptions: {
      headless: false,
      viewport: {
        width: 1280,
        height: 720,
      },
    },
  });

  try {
    await stagehand.init();
    console.log("✅ Stagehand with AI initialized successfully!");

    const page = stagehand.page;

    // 加载本地测试页面
    console.log("\n📍 Loading test page...");
    await page.goto(fileUrl);
    await page.waitForTimeout(2000);

    // 使用 AI 观察页面
    console.log("\n🔍 Step 1: Using AI to observe the page...");
    const observations = await page.observe({
      instruction: "Find all interactive elements on this page"
    });
    
    console.log("🤖 AI found these interactive elements:");
    observations.slice(0, 3).forEach((obs, index) => {
      console.log(`${index + 1}. ${obs.description}`);
    });

    // 使用 AI 执行操作
    console.log("\n🎯 Step 2: Using AI to interact with the page...");
    await page.act("Type 'AI-powered automation' in the search input field");
    await page.act("Click the search button");

    await page.waitForTimeout(1000);

    // 使用 AI 提取信息
    console.log("\n📊 Step 3: Using AI to extract information...");
    const pageInfo = await page.extract({
      instruction: "Extract the page title, search input value, and result text",
      schema: z.object({
        title: z.string(),
        searchValue: z.string(),
        resultText: z.string(),
      }),
    });

    console.log("🤖 AI extracted information:");
    console.log(`📄 Title: ${pageInfo.title}`);
    console.log(`🔍 Search Value: ${pageInfo.searchValue}`);
    console.log(`📝 Result: ${pageInfo.resultText}`);

    // 使用 AI 执行更多操作
    console.log("\n🧹 Step 4: Using AI to clear and test again...");
    await page.act("Click the clear button");
    await page.waitForTimeout(500);
    
    await page.act("Type 'Stagehand is awesome!' in the search box");
    await page.act("Click the search button");
    await page.waitForTimeout(1000);

    // 最终提取
    const finalInfo = await page.extract({
      instruction: "Get the current search result text",
      schema: z.object({
        result: z.string(),
      }),
    });

    console.log(`🎉 Final result: ${finalInfo.result}`);

    console.log("\n✨ AI demo completed successfully!");

  } catch (error) {
    console.error("❌ Error during AI demo:", error);
  } finally {
    console.log("\n🧹 Cleaning up...");
    await stagehand.close();
    console.log("✅ Stagehand closed successfully!");
  }
}

aiLocalDemo().catch(console.error);
