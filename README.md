# Cute

#### 介绍
你有没有想过，你每天用的浏览器其实可以完全是另一个样子？ 它可以更个性，是你喜欢的样子，而不是被别人规定好的样子。看见不爽的布局，一句话就能改；想加个好玩的功能，一句话就能实现。

它也可以更高效，帮你干掉所有重复劳动。需要手动复制粘贴的数据，一句话就能导出；每天要点的按钮，一句话就能自动搞定。

过去，个性化和效率需要两个甚至二十个不同的插件才能实现。

现在，你只需要一个CuTe。

它是一个All-in-One的浏览器AI插件，无论是满足你独一无二的个性化需求，还是提升你的生产力效率，都只是一句话的事。

别再忍受了，你的浏览器，本就该同时懂你的审美，也懂你的工作。

#### 软件架构
软件架构说明


#### 安装教程

1.  xxxx
2.  xxxx
3.  xxxx

#### 使用说明

1.  xxxx
2.  xxxx
3.  xxxx

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 特技

1.  使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
