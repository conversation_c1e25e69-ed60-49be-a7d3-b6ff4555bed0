import { chromium } from 'playwright';

async function testPlaywright() {
  console.log("🎭 Testing Playwright directly...");

  try {
    // 启动浏览器
    const browser = await chromium.launch({ 
      headless: false,
      viewport: { width: 1280, height: 720 }
    });
    
    console.log("✅ Browser launched successfully!");

    // 创建新页面
    const page = await browser.newPage();
    console.log("✅ New page created!");

    // 导航到网站
    console.log("📍 Navigating to example.com...");
    await page.goto('https://example.com');
    
    // 获取标题
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);

    // 等待一下
    console.log("⏳ Waiting 3 seconds...");
    await page.waitForTimeout(3000);

    // 导航到另一个网站
    console.log("📍 Navigating to httpbin.org...");
    await page.goto('https://httpbin.org');
    
    const newTitle = await page.title();
    console.log(`📄 New page title: ${newTitle}`);

    // 再等待一下
    console.log("⏳ Waiting 3 seconds...");
    await page.waitForTimeout(3000);

    console.log("🎉 Playwright test completed successfully!");

    // 关闭浏览器
    await browser.close();
    console.log("✅ Browser closed!");

  } catch (error) {
    console.error("❌ Error during Playwright test:", error);
  }
}

testPlaywright().catch(console.error);
